{
    "folders": [
        {
            "path": "rename_with_editor",
            "folder_exclude_patterns": [
                "*.egg-info",
                ".backups",
                ".DS_Store",
                ".git",
                ".hg",
                ".idea",
                ".svn",
                ".vscode",
                "__pycache__",
                ".uv-cache",
                "build",
                "dist",
                "env",
                "logs",
                "node_modules",
                "venv",
                ".venv",
            ],
            "file_exclude_patterns": [
                "*.log",
                "*.pyc",
                "*.pyo",
                "*.sublime-workspace",
                "*.swp",
                "*.tmp",
                ".DS_Store",
                ".gitignore",
            ]
        }
    ],
    "settings": {
        "tab_size": 4,
        "default_line_ending": "unix",
        "translate_tabs_to_spaces": true,
        "ensure_newline_at_eof_on_save": true,
        "trim_trailing_white_space_on_save": true,
        "python_interpreter": "uv run python",
        "python_formatter": "black",
        "python_linter": "flake8",
        "python_format_on_save": false
    },
    "build_systems": [
        {
            "name": "Rename With Editor (uv)",
            "cmd": ["uv", "run", "python", "src/main.py"],
            "file_regex": "^[ ]*File \"(...*?)\", line ([0-9]*)",
            "selector": "source.python",
            "shell": true,
            "working_dir": "${project_path}/rename_with_editor"
        }
    ]
}

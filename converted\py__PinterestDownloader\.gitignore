# Parent/Workspace .gitignore
# For managing files in a general development/notes parent directory.
# This is intended for a higher-level folder that might contain multiple projects.

# =======================================================
# DIRECTORIES
# =======================================================

# python
**/.cache/
**/.env/
**/.pytest_cache/
**/.venv/
**/__pycache__/
**/env/
**/venv/

# nodejs/react
**/node_modules/

# build and temp
**/build/
**/cache/
**/dist/
**/logs/
**/temp/
**/tmp/

# =======================================================
# EXTENSIONS
# =======================================================

# media
*.mp4
*.mkv
*.webm
*.mp3
*.wav
*.png

# ide
*.sublime-workspace
*.sublime_session
*.swp

# binaries
*.bin
*.dll
*.exe
*.pyc
*.pyo

# misc
*.DS_Store
*.blend1
*.ini.bak
*.ldb
*.log
*.pak
*.pickle
*.prv.ppk
*.prv.pub
*.tmp

# =======================================================
# EXPLICIT
# =======================================================

# filenames
**/.what-is-this.md
**/app.log.yml
**/quit.blend
**/Run History-1.5a.csv
**/Search History-1.5a.csv
**/Session-1.5a.backup.json
**/Session-1.5a.json

# paths
**/*sync-conflict*.*
**/user-data/**/ui_messages.json
**/.specstory/history/.what-is-this.md
# AI Systems - Instruction Sequence Executor

Multi-step LLM instruction sequence executor with template management system. Execute user prompts through sequences of different system instructions using multiple LLM models for diverse perspectives and step-by-step refinement.

## Features

- Execute prompts through multiple system instructions
- Support for multiple LLM models per step
- Template-based instruction sequences
- Cost tracking and structured JSON output
- Chain mode for sequential processing
- Aggregator sequences for result synthesis

## Quick Start

```bash
# Install dependencies
uv sync

# Run with default sequence
uv run python src/main.py --prompt "Your question here"

# Use specific sequence and models
uv run python src/main.py --sequence "0121" --models "gpt-4o,claude-3-haiku" --prompt "Your question"
```

## Core Components

- **main.py**: Core execution engine with LLM interaction via litellm
- **templates/**: Instruction template management system
- **Template Catalog**: Dynamic template discovery and sequence management
- **Chain Mode**: Sequential step processing with output chaining
- **Aggregator**: Result synthesis across multiple steps

## Configuration

Set environment variables for API access:
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `OPENROUTER_API_KEY`

## Template System

Templates are organized in stages:
- **Stage 1 (1000-1999)**: Prototyping/Testing
- **Stage 2 (2000-2999)**: Validated/Unplaced  
- **Stage 3 (3000-3999)**: Finalized/Production

Template naming: `NNNN-L-description.md` where NNNN is sequence ID and L is step order.

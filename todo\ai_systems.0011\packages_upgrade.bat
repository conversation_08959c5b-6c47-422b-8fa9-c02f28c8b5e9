@echo off
setlocal enabledelayedexpansion

:: Change to script directory
cd /d "%~dp0"

:: Check if uv is installed
where uv >nul 2>&1
if errorlevel 1 (
    echo Error: uv is not installed or not in PATH
    echo Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

echo Upgrading packages with uv...
echo.

:: Upgrade all dependencies
uv sync --upgrade
if errorlevel 1 (
    echo Error: Failed to upgrade packages
    pause
    exit /b 1
)

echo.
echo Packages upgraded successfully!
echo.
pause

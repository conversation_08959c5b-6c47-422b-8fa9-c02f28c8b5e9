"""
    pygments.lexers._lilypond_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    LilyPond builtins.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# Contents generated by the script lilypond-builtins-generator.ly
# found in the external/ directory of the source tree.

keywords = [
  "accepts",
  "addlyrics",
  "alias",
  "book",
  "bookpart",
  "chordmode",
  "chords",
  "consists",
  "context",
  "defaultchild",
  "denies",
  "description",
  "drummode",
  "drums",
  "etc",
  "figuremode",
  "figures",
  "header",
  "include",
  "inherit-acceptability",
  "language",
  "layout",
  "lyricmode",
  "lyricsto",
  "midi",
  "name",
  "new",
  "notemode",
  "paper",
  "remove",
  "score",
  "type",
  "version",
  "with",
]

clefs = [
  "C",
  "F",
  "G",
  "G2",
  "GG",
  "alto",
  "altovarC",
  "baritone",
  "baritonevarC",
  "baritonevarF",
  "bass",
  "blackmensural-c1",
  "blackmensural-c2",
  "blackmensural-c3",
  "blackmensural-c4",
  "blackmensural-c5",
  "french",
  "hufnagel-do-fa",
  "hufnagel-do1",
  "hufnagel-do2",
  "hufnagel-do3",
  "hufnagel-fa1",
  "hufnagel-fa2",
  "kievan-do",
  "medicaea-do1",
  "medicaea-do2",
  "medicaea-do3",
  "medicaea-fa1",
  "medicaea-fa2",
  "mensural-c1",
  "mensural-c2",
  "mensural-c3",
  "mensural-c4",
  "mensural-c5",
  "mensural-f",
  "mensural-g",
  "mezzosoprano",
  "moderntab",
  "neomensural-c1",
  "neomensural-c2",
  "neomensural-c3",
  "neomensural-c4",
  "neomensural-c5",
  "percussion",
  "petrucci-c1",
  "petrucci-c2",
  "petrucci-c3",
  "petrucci-c4",
  "petrucci-c5",
  "petrucci-f",
  "petrucci-f2",
  "petrucci-f3",
  "petrucci-f4",
  "petrucci-f5",
  "petrucci-g",
  "petrucci-g1",
  "petrucci-g2",
  "soprano",
  "subbass",
  "tab",
  "tenor",
  "tenorG",
  "tenorvarC",
  "treble",
  "varC",
  "varbaritone",
  "varpercussion",
  "vaticana-do1",
  "vaticana-do2",
  "vaticana-do3",
  "vaticana-fa1",
  "vaticana-fa2",
  "violin",
]

scales = [
  "aeolian",
  "dorian",
  "ionian",
  "locrian",
  "lydian",
  "major",
  "minor",
  "mixolydian",
  "phrygian",
]

repeat_types = [
  "percent",
  "segno",
  "tremolo",
  "unfold",
  "volta",
]

units = [
  "cm",
  "in",
  "mm",
  "pt",
  "staff-space",
]

chord_modifiers = [
  "aug",
  "dim",
  "m",
  "maj",
]

pitch_language_names = [
  "arabic",
  "catalan",
  "català",
  "deutsch",
  "english",
  "espanol",
  "español",
  "français",
  "italiano",
  "nederlands",
  "norsk",
  "portugues",
  "português",
  "suomi",
  "svenska",
  "vlaams",
]

pitches = [
  "R",
  "a",
  "a-flat",
  "a-flatflat",
  "a-natural",
  "a-sharp",
  "a-sharpsharp",
  "ab",
  "acousticbassdrum",
  "acousticsnare",
  "ad",
  "adb",
  "add",
  "aeh",
  "aes",
  "aeseh",
  "aeses",
  "aess",
  "aesseh",
  "aessess",
  "af",
  "aff",
  "afhb",
  "afhd",
  "agh",
  "agl",
  "ah",
  "aih",
  "ais",
  "aisih",
  "aisis",
  "aiss",
  "aissih",
  "aississ",
  "aqf",
  "aqs",
  "as",
  "asah",
  "asas",
  "aseh",
  "ases",
  "ashb",
  "ashd",
  "ass",
  "asseh",
  "assess",
  "atqb",
  "atqd",
  "atqf",
  "atqs",
  "ax",
  "b",
  "b-flat",
  "b-flatflat",
  "b-natural",
  "b-sharp",
  "b-sharpsharp",
  "bassdrum",
  "bb",
  "bd",
  "bda",
  "bdb",
  "bdd",
  "beh",
  "bes",
  "beseh",
  "beses",
  "bess",
  "bf",
  "bff",
  "bfhb",
  "bfhd",
  "bih",
  "bis",
  "bisih",
  "bisis",
  "boh",
  "bohm",
  "boho",
  "bol",
  "bolm",
  "bolo",
  "bqf",
  "bqs",
  "bs",
  "bshb",
  "bshd",
  "bss",
  "btqb",
  "btqd",
  "btqf",
  "btqs",
  "bx",
  "c",
  "c-flat",
  "c-flatflat",
  "c-natural",
  "c-sharp",
  "c-sharpsharp",
  "cab",
  "cabasa",
  "cb",
  "cd",
  "cdb",
  "cdd",
  "ceh",
  "ces",
  "ceseh",
  "ceses",
  "cess",
  "cesseh",
  "cessess",
  "cf",
  "cff",
  "cfhb",
  "cfhd",
  "cgh",
  "cghm",
  "cgho",
  "cgl",
  "cglm",
  "cglo",
  "chinesecymbal",
  "cih",
  "cis",
  "cisih",
  "cisis",
  "ciss",
  "cissih",
  "cississ",
  "cl",
  "claves",
  "closedhihat",
  "cowbell",
  "cqf",
  "cqs",
  "crashcymbal",
  "crashcymbala",
  "crashcymbalb",
  "cs",
  "cshb",
  "cshd",
  "css",
  "ctqb",
  "ctqd",
  "ctqf",
  "ctqs",
  "cuim",
  "cuio",
  "cx",
  "cymc",
  "cymca",
  "cymcb",
  "cymch",
  "cymr",
  "cymra",
  "cymrb",
  "cyms",
  "d",
  "d-flat",
  "d-flatflat",
  "d-natural",
  "d-sharp",
  "d-sharpsharp",
  "db",
  "dd",
  "ddb",
  "ddd",
  "deh",
  "des",
  "deseh",
  "deses",
  "dess",
  "desseh",
  "dessess",
  "df",
  "dff",
  "dfhb",
  "dfhd",
  "dih",
  "dis",
  "disih",
  "disis",
  "diss",
  "dissih",
  "dississ",
  "do",
  "dob",
  "dobb",
  "dobhb",
  "dobqt",
  "dobsb",
  "dobtqt",
  "docb",
  "docs",
  "dod",
  "dodd",
  "dodsd",
  "dohb",
  "dohk",
  "dok",
  "dokhk",
  "dokk",
  "doqb",
  "doqd",
  "doqs",
  "dos",
  "dosb",
  "dosd",
  "dosqt",
  "doss",
  "dostqt",
  "dotcb",
  "dotcs",
  "dotqb",
  "dotqd",
  "dotqs",
  "dox",
  "dqf",
  "dqs",
  "ds",
  "dshb",
  "dshd",
  "dss",
  "dtqb",
  "dtqd",
  "dtqf",
  "dtqs",
  "dx",
  "e",
  "e-flat",
  "e-flatflat",
  "e-natural",
  "e-sharp",
  "e-sharpsharp",
  "eb",
  "ed",
  "edb",
  "edd",
  "eeh",
  "ees",
  "eeseh",
  "eeses",
  "eess",
  "eesseh",
  "eessess",
  "ef",
  "eff",
  "efhb",
  "efhd",
  "eh",
  "eih",
  "eis",
  "eisih",
  "eisis",
  "eiss",
  "eissih",
  "eississ",
  "electricsnare",
  "eqf",
  "eqs",
  "es",
  "eseh",
  "eses",
  "eshb",
  "eshd",
  "ess",
  "esseh",
  "essess",
  "etqb",
  "etqd",
  "etqf",
  "etqs",
  "ex",
  "f",
  "f-flat",
  "f-flatflat",
  "f-natural",
  "f-sharp",
  "f-sharpsharp",
  "fa",
  "fab",
  "fabb",
  "fabhb",
  "fabqt",
  "fabsb",
  "fabtqt",
  "facb",
  "facs",
  "fad",
  "fadd",
  "fadsd",
  "fahb",
  "fahk",
  "fak",
  "fakhk",
  "fakk",
  "faqb",
  "faqd",
  "faqs",
  "fas",
  "fasb",
  "fasd",
  "fasqt",
  "fass",
  "fastqt",
  "fatcb",
  "fatcs",
  "fatqb",
  "fatqd",
  "fatqs",
  "fax",
  "fb",
  "fd",
  "fdb",
  "fdd",
  "feh",
  "fes",
  "feseh",
  "feses",
  "fess",
  "fesseh",
  "fessess",
  "ff",
  "fff",
  "ffhb",
  "ffhd",
  "fih",
  "fis",
  "fisih",
  "fisis",
  "fiss",
  "fissih",
  "fississ",
  "fqf",
  "fqs",
  "fs",
  "fshb",
  "fshd",
  "fss",
  "ftqb",
  "ftqd",
  "ftqf",
  "ftqs",
  "fx",
  "g",
  "g-flat",
  "g-flatflat",
  "g-natural",
  "g-sharp",
  "g-sharpsharp",
  "gb",
  "gd",
  "gdb",
  "gdd",
  "geh",
  "ges",
  "geseh",
  "geses",
  "gess",
  "gesseh",
  "gessess",
  "gf",
  "gff",
  "gfhb",
  "gfhd",
  "gih",
  "gis",
  "gisih",
  "gisis",
  "giss",
  "gissih",
  "gississ",
  "gqf",
  "gqs",
  "gs",
  "gshb",
  "gshd",
  "gss",
  "gtqb",
  "gtqd",
  "gtqf",
  "gtqs",
  "gui",
  "guil",
  "guiro",
  "guis",
  "gx",
  "h",
  "halfopenhihat",
  "handclap",
  "hc",
  "heh",
  "heseh",
  "heses",
  "hesseh",
  "hessess",
  "hh",
  "hhc",
  "hhho",
  "hho",
  "hhp",
  "hiagogo",
  "hibongo",
  "hiconga",
  "highfloortom",
  "hightom",
  "hih",
  "hihat",
  "himidtom",
  "his",
  "hisidestick",
  "hisih",
  "hisis",
  "hiss",
  "hissih",
  "hississ",
  "hitimbale",
  "hiwoodblock",
  "la",
  "lab",
  "labb",
  "labhb",
  "labqt",
  "labsb",
  "labtqt",
  "lacb",
  "lacs",
  "lad",
  "ladd",
  "ladsd",
  "lahb",
  "lahk",
  "lak",
  "lakhk",
  "lakk",
  "laqb",
  "laqd",
  "laqs",
  "las",
  "lasb",
  "lasd",
  "lasqt",
  "lass",
  "lastqt",
  "latcb",
  "latcs",
  "latqb",
  "latqd",
  "latqs",
  "lax",
  "loagogo",
  "lobongo",
  "loconga",
  "longguiro",
  "longwhistle",
  "losidestick",
  "lotimbale",
  "lowfloortom",
  "lowmidtom",
  "lowoodblock",
  "lowtom",
  "mar",
  "maracas",
  "mi",
  "mib",
  "mibb",
  "mibhb",
  "mibqt",
  "mibsb",
  "mibtqt",
  "micb",
  "mics",
  "mid",
  "midd",
  "midsd",
  "mihb",
  "mihk",
  "mik",
  "mikhk",
  "mikk",
  "miqb",
  "miqd",
  "miqs",
  "mis",
  "misb",
  "misd",
  "misqt",
  "miss",
  "mistqt",
  "mitcb",
  "mitcs",
  "mitqb",
  "mitqd",
  "mitqs",
  "mix",
  "mutecuica",
  "mutehibongo",
  "mutehiconga",
  "mutelobongo",
  "muteloconga",
  "mutetriangle",
  "opencuica",
  "openhibongo",
  "openhiconga",
  "openhihat",
  "openlobongo",
  "openloconga",
  "opentriangle",
  "pedalhihat",
  "r",
  "rb",
  "re",
  "reb",
  "rebb",
  "rebhb",
  "rebqt",
  "rebsb",
  "rebtqt",
  "recb",
  "recs",
  "red",
  "redd",
  "redsd",
  "rehb",
  "rehk",
  "rek",
  "rekhk",
  "rekk",
  "reqb",
  "reqd",
  "reqs",
  "res",
  "resb",
  "resd",
  "resqt",
  "ress",
  "restqt",
  "retcb",
  "retcs",
  "retqb",
  "retqd",
  "retqs",
  "rex",
  "ridebell",
  "ridecymbal",
  "ridecymbala",
  "ridecymbalb",
  "ré",
  "réb",
  "rébb",
  "rébsb",
  "réd",
  "rédd",
  "rédsd",
  "résb",
  "résd",
  "réx",
  "shortguiro",
  "shortwhistle",
  "si",
  "sib",
  "sibb",
  "sibhb",
  "sibqt",
  "sibsb",
  "sibtqt",
  "sicb",
  "sics",
  "sid",
  "sidd",
  "sidestick",
  "sidsd",
  "sihb",
  "sihk",
  "sik",
  "sikhk",
  "sikk",
  "siqb",
  "siqd",
  "siqs",
  "sis",
  "sisb",
  "sisd",
  "sisqt",
  "siss",
  "sistqt",
  "sitcb",
  "sitcs",
  "sitqb",
  "sitqd",
  "sitqs",
  "six",
  "sn",
  "sna",
  "snare",
  "sne",
  "sol",
  "solb",
  "solbb",
  "solbhb",
  "solbqt",
  "solbsb",
  "solbtqt",
  "solcb",
  "solcs",
  "sold",
  "soldd",
  "soldsd",
  "solhb",
  "solhk",
  "solk",
  "solkhk",
  "solkk",
  "solqb",
  "solqd",
  "solqs",
  "sols",
  "solsb",
  "solsd",
  "solsqt",
  "solss",
  "solstqt",
  "soltcb",
  "soltcs",
  "soltqb",
  "soltqd",
  "soltqs",
  "solx",
  "splashcymbal",
  "ss",
  "ssh",
  "ssl",
  "tamb",
  "tambourine",
  "timh",
  "timl",
  "tomfh",
  "tomfl",
  "tomh",
  "toml",
  "tommh",
  "tomml",
  "tri",
  "triangle",
  "trim",
  "trio",
  "tt",
  "vibraslap",
  "vibs",
  "wbh",
  "wbl",
  "whl",
  "whs",
]

music_functions = [
  "=",
  "absolute",
  "acciaccatura",
  "accidentalStyle",
  "addChordShape",
  "addInstrumentDefinition",
  "addQuote",
  "after",
  "afterGrace",
  "allowPageTurn",
  "allowVoltaHook",
  "alterBroken",
  "alternative",
  "ambitusAfter",
  "appendToTag",
  "applyContext",
  "applyMusic",
  "applyOutput",
  "appoggiatura",
  "assertBeamQuant",
  "assertBeamSlope",
  "autoChange",
  "balloonGrobText",
  "balloonText",
  "bar",
  "barNumberCheck",
  "beamExceptions",
  "bendAfter",
  "bendHold",
  "bendStartLevel",
  "bookOutputName",
  "bookOutputSuffix",
  "breathe",
  "caesura",
  "change",
  "chordRepeats",
  "clef",
  "codaMark",
  "compoundMeter",
  "compressMMRests",
  "crossStaff",
  "cueClef",
  "cueClefUnset",
  "cueDuring",
  "cueDuringWithClef",
  "deadNote",
  "defineBarLine",
  "displayLilyMusic",
  "displayMusic",
  "displayScheme",
  "dropNote",
  "enablePolymeter",
  "endSpanners",
  "eventChords",
  "featherDurations",
  "finger",
  "fixed",
  "footnote",
  "grace",
  "grobdescriptions",
  "harmonicByFret",
  "harmonicByRatio",
  "harmonicNote",
  "harmonicsOn",
  "hide",
  "inStaffSegno",
  "incipit",
  "inherit-acceptability",
  "instrumentSwitch",
  "inversion",
  "invertChords",
  "jump",
  "keepWithTag",
  "key",
  "killCues",
  "label",
  "language",
  "languageRestore",
  "languageSaveAndChange",
  "magnifyMusic",
  "magnifyStaff",
  "makeClusters",
  "makeDefaultStringTuning",
  "mark",
  "markupMap",
  "modalInversion",
  "modalTranspose",
  "musicMap",
  "noPageBreak",
  "noPageTurn",
  "octaveCheck",
  "offset",
  "omit",
  "once",
  "ottava",
  "override",
  "overrideProperty",
  "overrideTimeSignatureSettings",
  "pageBreak",
  "pageTurn",
  "palmMute",
  "palmMuteOn",
  "parallelMusic",
  "parenthesize",
  "partCombine",
  "partCombineDown",
  "partCombineForce",
  "partCombineUp",
  "partial",
  "phrasingSlurDashPattern",
  "pitchedTrill",
  "pointAndClickOff",
  "pointAndClickOn",
  "pointAndClickTypes",
  "preBend",
  "preBendHold",
  "propertyOverride",
  "propertyRevert",
  "propertySet",
  "propertyTweak",
  "propertyUnset",
  "pushToTag",
  "quoteDuring",
  "raiseNote",
  "reduceChords",
  "relative",
  "removeWithTag",
  "repeat",
  "resetRelativeOctave",
  "retrograde",
  "revert",
  "revertTimeSignatureSettings",
  "rightHandFinger",
  "scaleDurations",
  "sectionLabel",
  "segnoMark",
  "set",
  "settingsFrom",
  "shape",
  "shiftDurations",
  "single",
  "skip",
  "slashedGrace",
  "slurDashPattern",
  "staffHighlight",
  "storePredefinedDiagram",
  "stringTuning",
  "styledNoteHeads",
  "tabChordRepeats",
  "tabChordRepetition",
  "tag",
  "tagGroup",
  "tempo",
  "temporary",
  "textEndMark",
  "textMark",
  "tieDashPattern",
  "time",
  "times",
  "tocItem",
  "transpose",
  "transposedCueDuring",
  "transposition",
  "tuplet",
  "tupletSpan",
  "tweak",
  "undo",
  "unfoldRepeats",
  "unfolded",
  "unset",
  "voices",
  "void",
  "volta",
  "vshape",
  "withMusicProperty",
  "xNote",
]

dynamics = [
  "!",
  "<",
  ">",
  "cr",
  "cresc",
  "decr",
  "decresc",
  "dim",
  "endcr",
  "endcresc",
  "enddecr",
  "enddecresc",
  "enddim",
  "f",
  "ff",
  "fff",
  "ffff",
  "fffff",
  "fp",
  "fz",
  "mf",
  "mp",
  "n",
  "p",
  "pp",
  "ppp",
  "pppp",
  "ppppp",
  "rfz",
  "sf",
  "sff",
  "sfp",
  "sfz",
  "sp",
  "spp",
]

articulations = [
  "(",
  ")",
  "-",
  "[",
  "]",
  "^",
  "accent",
  "arpeggio",
  "breakDynamicSpan",
  "coda",
  "dashBang",
  "dashDash",
  "dashDot",
  "dashHat",
  "dashLarger",
  "dashPlus",
  "dashUnderscore",
  "downbow",
  "downmordent",
  "downprall",
  "episemFinis",
  "episemInitium",
  "espressivo",
  "fermata",
  "flageolet",
  "glide",
  "glissando",
  "halfopen",
  "harmonic",
  "haydnturn",
  "henzelongfermata",
  "henzeshortfermata",
  "laissezVibrer",
  "lheel",
  "lineprall",
  "longfermata",
  "ltoe",
  "marcato",
  "mordent",
  "noBeam",
  "open",
  "portato",
  "prall",
  "pralldown",
  "prallmordent",
  "prallprall",
  "prallup",
  "repeatTie",
  "reverseturn",
  "rheel",
  "rtoe",
  "segno",
  "shortfermata",
  "signumcongruentiae",
  "slashturn",
  "snappizzicato",
  "sostenutoOff",
  "sostenutoOn",
  "staccatissimo",
  "staccato",
  "startGraceSlur",
  "startGroup",
  "startTextSpan",
  "startTrillSpan",
  "stopGraceSlur",
  "stopGroup",
  "stopTextSpan",
  "stopTrillSpan",
  "stopped",
  "sustainOff",
  "sustainOn",
  "tenuto",
  "thumb",
  "treCorde",
  "trill",
  "turn",
  "unaCorda",
  "upbow",
  "upmordent",
  "upprall",
  "varcoda",
  "verylongfermata",
  "veryshortfermata",
  "vowelTransition",
  "~",
]

music_commands = [
  "[",
  "]",
  "aikenHeads",
  "aikenHeadsMinor",
  "aikenThinHeads",
  "aikenThinHeadsMinor",
  "allowBreak",
  "arabicStringNumbers",
  "arpeggioArrowDown",
  "arpeggioArrowUp",
  "arpeggioBracket",
  "arpeggioNormal",
  "arpeggioParenthesis",
  "arpeggioParenthesisDashed",
  "autoBeamOff",
  "autoBeamOn",
  "autoBreaksOff",
  "autoBreaksOn",
  "autoLineBreaksOff",
  "autoLineBreaksOn",
  "autoPageBreaksOff",
  "autoPageBreaksOn",
  "balloonLengthOff",
  "balloonLengthOn",
  "bassFigureExtendersOff",
  "bassFigureExtendersOn",
  "bassFigureStaffAlignmentDown",
  "bassFigureStaffAlignmentNeutral",
  "bassFigureStaffAlignmentUp",
  "break",
  "cadenzaOff",
  "cadenzaOn",
  "compressEmptyMeasures",
  "crescHairpin",
  "crescTextCresc",
  "deadNotesOff",
  "deadNotesOn",
  "defaultNoteHeads",
  "defaultTimeSignature",
  "deprecatedcresc",
  "deprecateddim",
  "deprecatedendcresc",
  "deprecatedenddim",
  "dimHairpin",
  "dimTextDecr",
  "dimTextDecresc",
  "dimTextDim",
  "dotsDown",
  "dotsNeutral",
  "dotsUp",
  "dynamicDown",
  "dynamicNeutral",
  "dynamicUp",
  "easyHeadsOff",
  "easyHeadsOn",
  "endSkipNCs",
  "expandEmptyMeasures",
  "fine",
  "frenchChords",
  "funkHeads",
  "funkHeadsMinor",
  "germanChords",
  "harmonicsOff",
  "hideNotes",
  "hideSplitTiedTabNotes",
  "hideStaffSwitch",
  "huge",
  "ignatzekExceptionMusic",
  "improvisationOff",
  "improvisationOn",
  "italianChords",
  "kievanOff",
  "kievanOn",
  "large",
  "markLengthOff",
  "markLengthOn",
  "medianChordGridStyle",
  "melisma",
  "melismaEnd",
  "mergeDifferentlyDottedOff",
  "mergeDifferentlyDottedOn",
  "mergeDifferentlyHeadedOff",
  "mergeDifferentlyHeadedOn",
  "newSpacingSection",
  "noBreak",
  "normalsize",
  "numericTimeSignature",
  "oneVoice",
  "palmMuteOff",
  "partCombineApart",
  "partCombineAutomatic",
  "partCombineChords",
  "partCombineSoloI",
  "partCombineSoloII",
  "partCombineUnisono",
  "phrasingSlurDashed",
  "phrasingSlurDotted",
  "phrasingSlurDown",
  "phrasingSlurHalfDashed",
  "phrasingSlurHalfSolid",
  "phrasingSlurNeutral",
  "phrasingSlurSolid",
  "phrasingSlurUp",
  "predefinedFretboardsOff",
  "predefinedFretboardsOn",
  "romanStringNumbers",
  "sacredHarpHeads",
  "sacredHarpHeadsMinor",
  "section",
  "semiGermanChords",
  "setDefaultDurationToQuarter",
  "shiftOff",
  "shiftOn",
  "shiftOnn",
  "shiftOnnn",
  "showSplitTiedTabNotes",
  "showStaffSwitch",
  "skipNC",
  "skipNCs",
  "slurDashed",
  "slurDotted",
  "slurDown",
  "slurHalfDashed",
  "slurHalfSolid",
  "slurNeutral",
  "slurSolid",
  "slurUp",
  "small",
  "southernHarmonyHeads",
  "southernHarmonyHeadsMinor",
  "startAcciaccaturaMusic",
  "startAppoggiaturaMusic",
  "startGraceMusic",
  "startMeasureCount",
  "startMeasureSpanner",
  "startSlashedGraceMusic",
  "startStaff",
  "stemDown",
  "stemNeutral",
  "stemUp",
  "stopAcciaccaturaMusic",
  "stopAppoggiaturaMusic",
  "stopGraceMusic",
  "stopMeasureCount",
  "stopMeasureSpanner",
  "stopSlashedGraceMusic",
  "stopStaff",
  "stopStaffHighlight",
  "tabFullNotation",
  "teeny",
  "textLengthOff",
  "textLengthOn",
  "textSpannerDown",
  "textSpannerNeutral",
  "textSpannerUp",
  "tieDashed",
  "tieDotted",
  "tieDown",
  "tieHalfDashed",
  "tieHalfSolid",
  "tieNeutral",
  "tieSolid",
  "tieUp",
  "tiny",
  "tupletDown",
  "tupletNeutral",
  "tupletUp",
  "unHideNotes",
  "voiceFour",
  "voiceFourStyle",
  "voiceNeutralStyle",
  "voiceOne",
  "voiceOneStyle",
  "voiceThree",
  "voiceThreeStyle",
  "voiceTwo",
  "voiceTwoStyle",
  "walkerHeads",
  "walkerHeadsMinor",
  "xNotesOff",
  "xNotesOn",
  "|",
  "~",
]

markup_commands = [
  "abs-fontsize",
  "accidental",
  "align-on-other",
  "arrow-head",
  "auto-footnote",
  "backslashed-digit",
  "beam",
  "bold",
  "box",
  "bracket",
  "caps",
  "center-align",
  "center-column",
  "char",
  "circle",
  "coda",
  "column",
  "column-lines",
  "combine",
  "compound-meter",
  "concat",
  "conditional-trill-markup",
  "customTabClef",
  "dir-column",
  "discant",
  "doubleflat",
  "doublesharp",
  "draw-circle",
  "draw-dashed-line",
  "draw-dotted-line",
  "draw-hline",
  "draw-line",
  "draw-squiggle-line",
  "dynamic",
  "ellipse",
  "epsfile",
  "eyeglasses",
  "fermata",
  "figured-bass",
  "fill-line",
  "fill-with-pattern",
  "filled-box",
  "finger",
  "first-visible",
  "flat",
  "fontCaps",
  "fontsize",
  "footnote",
  "fraction",
  "freeBass",
  "fret-diagram",
  "fret-diagram-terse",
  "fret-diagram-verbose",
  "fromproperty",
  "general-align",
  "halign",
  "harp-pedal",
  "hbracket",
  "hcenter-in",
  "hspace",
  "huge",
  "if",
  "italic",
  "justified-lines",
  "justify",
  "justify-field",
  "justify-line",
  "justify-string",
  "large",
  "larger",
  "left-align",
  "left-brace",
  "left-column",
  "line",
  "lookup",
  "lower",
  "magnify",
  "map-markup-commands",
  "markalphabet",
  "markletter",
  "markup",
  "markuplist",
  "medium",
  "multi-measure-rest-by-number",
  "musicglyph",
  "natural",
  "normal-size-sub",
  "normal-size-super",
  "normal-text",
  "normalsize",
  "note",
  "note-by-number",
  "null",
  "number",
  "on-the-fly",
  "oval",
  "overlay",
  "override",
  "override-lines",
  "overtie",
  "pad-around",
  "pad-markup",
  "pad-to-box",
  "pad-x",
  "page-link",
  "page-ref",
  "parenthesize",
  "path",
  "pattern",
  "polygon",
  "postscript",
  "property-recursive",
  "put-adjacent",
  "raise",
  "replace",
  "rest",
  "rest-by-number",
  "rhythm",
  "right-align",
  "right-brace",
  "right-column",
  "roman",
  "rotate",
  "rounded-box",
  "sans",
  "scale",
  "score",
  "score-lines",
  "segno",
  "semiflat",
  "semisharp",
  "sesquiflat",
  "sesquisharp",
  "sharp",
  "simple",
  "slashed-digit",
  "small",
  "smallCaps",
  "smaller",
  "stdBass",
  "stdBassIV",
  "stdBassV",
  "stdBassVI",
  "stencil",
  "string-lines",
  "strut",
  "sub",
  "super",
  "table",
  "table-of-contents",
  "teeny",
  "text",
  "tie",
  "tied-lyric",
  "tiny",
  "translate",
  "translate-scaled",
  "transparent",
  "triangle",
  "typewriter",
  "underline",
  "undertie",
  "unless",
  "upright",
  "varcoda",
  "vcenter",
  "verbatim-file",
  "vspace",
  "whiteout",
  "with-color",
  "with-dimension",
  "with-dimension-from",
  "with-dimensions",
  "with-dimensions-from",
  "with-link",
  "with-outline",
  "with-string-transformer",
  "with-true-dimension",
  "with-true-dimensions",
  "with-url",
  "woodwind-diagram",
  "wordwrap",
  "wordwrap-field",
  "wordwrap-internal",
  "wordwrap-lines",
  "wordwrap-string",
  "wordwrap-string-internal",
]

grobs = [
  "Accidental",
  "AccidentalCautionary",
  "AccidentalPlacement",
  "AccidentalSuggestion",
  "Ambitus",
  "AmbitusAccidental",
  "AmbitusLine",
  "AmbitusNoteHead",
  "Arpeggio",
  "BalloonText",
  "BarLine",
  "BarNumber",
  "BassFigure",
  "BassFigureAlignment",
  "BassFigureAlignmentPositioning",
  "BassFigureBracket",
  "BassFigureContinuation",
  "BassFigureLine",
  "Beam",
  "BendAfter",
  "BendSpanner",
  "BreakAlignGroup",
  "BreakAlignment",
  "BreathingSign",
  "CaesuraScript",
  "CenteredBarNumber",
  "CenteredBarNumberLineSpanner",
  "ChordName",
  "ChordSquare",
  "Clef",
  "ClefModifier",
  "ClusterSpanner",
  "ClusterSpannerBeacon",
  "CodaMark",
  "CombineTextScript",
  "ControlPoint",
  "ControlPolygon",
  "CueClef",
  "CueEndClef",
  "Custos",
  "Divisio",
  "DotColumn",
  "Dots",
  "DoublePercentRepeat",
  "DoublePercentRepeatCounter",
  "DoubleRepeatSlash",
  "DurationLine",
  "DynamicLineSpanner",
  "DynamicText",
  "DynamicTextSpanner",
  "Episema",
  "FingerGlideSpanner",
  "Fingering",
  "FingeringColumn",
  "Flag",
  "Footnote",
  "FretBoard",
  "Glissando",
  "GraceSpacing",
  "GridChordName",
  "GridLine",
  "GridPoint",
  "Hairpin",
  "HorizontalBracket",
  "HorizontalBracketText",
  "InstrumentName",
  "InstrumentSwitch",
  "JumpScript",
  "KeyCancellation",
  "KeySignature",
  "KievanLigature",
  "LaissezVibrerTie",
  "LaissezVibrerTieColumn",
  "LedgerLineSpanner",
  "LeftEdge",
  "LigatureBracket",
  "LyricExtender",
  "LyricHyphen",
  "LyricRepeatCount",
  "LyricSpace",
  "LyricText",
  "MeasureCounter",
  "MeasureGrouping",
  "MeasureSpanner",
  "MelodyItem",
  "MensuralLigature",
  "MetronomeMark",
  "MultiMeasureRest",
  "MultiMeasureRestNumber",
  "MultiMeasureRestScript",
  "MultiMeasureRestText",
  "NonMusicalPaperColumn",
  "NoteCollision",
  "NoteColumn",
  "NoteHead",
  "NoteName",
  "NoteSpacing",
  "OttavaBracket",
  "PaperColumn",
  "Parentheses",
  "PercentRepeat",
  "PercentRepeatCounter",
  "PhrasingSlur",
  "PianoPedalBracket",
  "RehearsalMark",
  "RepeatSlash",
  "RepeatTie",
  "RepeatTieColumn",
  "Rest",
  "RestCollision",
  "Script",
  "ScriptColumn",
  "ScriptRow",
  "SectionLabel",
  "SegnoMark",
  "SignumRepetitionis",
  "Slur",
  "SostenutoPedal",
  "SostenutoPedalLineSpanner",
  "SpacingSpanner",
  "SpanBar",
  "SpanBarStub",
  "StaffEllipsis",
  "StaffGrouper",
  "StaffHighlight",
  "StaffSpacing",
  "StaffSymbol",
  "StanzaNumber",
  "Stem",
  "StemStub",
  "StemTremolo",
  "StringNumber",
  "StrokeFinger",
  "SustainPedal",
  "SustainPedalLineSpanner",
  "System",
  "SystemStartBar",
  "SystemStartBrace",
  "SystemStartBracket",
  "SystemStartSquare",
  "TabNoteHead",
  "TextMark",
  "TextScript",
  "TextSpanner",
  "Tie",
  "TieColumn",
  "TimeSignature",
  "TrillPitchAccidental",
  "TrillPitchGroup",
  "TrillPitchHead",
  "TrillPitchParentheses",
  "TrillSpanner",
  "TupletBracket",
  "TupletNumber",
  "UnaCordaPedal",
  "UnaCordaPedalLineSpanner",
  "VaticanaLigature",
  "VerticalAlignment",
  "VerticalAxisGroup",
  "VoiceFollower",
  "VoltaBracket",
  "VoltaBracketSpanner",
  "VowelTransition",
]

contexts = [
  "ChoirStaff",
  "ChordGrid",
  "ChordGridScore",
  "ChordNames",
  "CueVoice",
  "Devnull",
  "DrumStaff",
  "DrumVoice",
  "Dynamics",
  "FiguredBass",
  "FretBoards",
  "Global",
  "GrandStaff",
  "GregorianTranscriptionLyrics",
  "GregorianTranscriptionStaff",
  "GregorianTranscriptionVoice",
  "InternalGregorianStaff",
  "KievanStaff",
  "KievanVoice",
  "Lyrics",
  "MensuralStaff",
  "MensuralVoice",
  "NoteNames",
  "NullVoice",
  "OneStaff",
  "PetrucciStaff",
  "PetrucciVoice",
  "PianoStaff",
  "RhythmicStaff",
  "Score",
  "Staff",
  "StaffGroup",
  "StandaloneRhythmScore",
  "StandaloneRhythmStaff",
  "StandaloneRhythmVoice",
  "TabStaff",
  "TabVoice",
  "Timing",
  "VaticanaLyrics",
  "VaticanaStaff",
  "VaticanaVoice",
  "Voice",
]

translators = [
  "Accidental_engraver",
  "Alteration_glyph_engraver",
  "Ambitus_engraver",
  "Arpeggio_engraver",
  "Auto_beam_engraver",
  "Axis_group_engraver",
  "Balloon_engraver",
  "Bar_engraver",
  "Bar_number_engraver",
  "Beam_collision_engraver",
  "Beam_engraver",
  "Beam_performer",
  "Beat_engraver",
  "Beat_performer",
  "Bend_engraver",
  "Bend_spanner_engraver",
  "Break_align_engraver",
  "Breathing_sign_engraver",
  "Caesura_engraver",
  "Centered_bar_number_align_engraver",
  "Chord_name_engraver",
  "Chord_square_engraver",
  "Chord_tremolo_engraver",
  "Clef_engraver",
  "Cluster_spanner_engraver",
  "Collision_engraver",
  "Completion_heads_engraver",
  "Completion_rest_engraver",
  "Concurrent_hairpin_engraver",
  "Control_track_performer",
  "Cue_clef_engraver",
  "Current_chord_text_engraver",
  "Custos_engraver",
  "Divisio_engraver",
  "Dot_column_engraver",
  "Dots_engraver",
  "Double_percent_repeat_engraver",
  "Drum_note_performer",
  "Drum_notes_engraver",
  "Duration_line_engraver",
  "Dynamic_align_engraver",
  "Dynamic_engraver",
  "Dynamic_performer",
  "Episema_engraver",
  "Extender_engraver",
  "Figured_bass_engraver",
  "Figured_bass_position_engraver",
  "Finger_glide_engraver",
  "Fingering_column_engraver",
  "Fingering_engraver",
  "Font_size_engraver",
  "Footnote_engraver",
  "Forbid_line_break_engraver",
  "Fretboard_engraver",
  "Glissando_engraver",
  "Grace_auto_beam_engraver",
  "Grace_beam_engraver",
  "Grace_engraver",
  "Grace_spacing_engraver",
  "Grid_chord_name_engraver",
  "Grid_line_span_engraver",
  "Grid_point_engraver",
  "Grob_pq_engraver",
  "Horizontal_bracket_engraver",
  "Hyphen_engraver",
  "Instrument_name_engraver",
  "Instrument_switch_engraver",
  "Jump_engraver",
  "Keep_alive_together_engraver",
  "Key_engraver",
  "Key_performer",
  "Kievan_ligature_engraver",
  "Laissez_vibrer_engraver",
  "Ledger_line_engraver",
  "Ligature_bracket_engraver",
  "Lyric_engraver",
  "Lyric_performer",
  "Lyric_repeat_count_engraver",
  "Mark_engraver",
  "Mark_performer",
  "Mark_tracking_translator",
  "Measure_counter_engraver",
  "Measure_grouping_engraver",
  "Measure_spanner_engraver",
  "Melody_engraver",
  "Mensural_ligature_engraver",
  "Merge_mmrest_numbers_engraver",
  "Merge_rests_engraver",
  "Metronome_mark_engraver",
  "Midi_control_change_performer",
  "Multi_measure_rest_engraver",
  "New_fingering_engraver",
  "Non_musical_script_column_engraver",
  "Note_head_line_engraver",
  "Note_heads_engraver",
  "Note_name_engraver",
  "Note_performer",
  "Note_spacing_engraver",
  "Ottava_spanner_engraver",
  "Output_property_engraver",
  "Page_turn_engraver",
  "Paper_column_engraver",
  "Parenthesis_engraver",
  "Part_combine_engraver",
  "Percent_repeat_engraver",
  "Phrasing_slur_engraver",
  "Piano_pedal_align_engraver",
  "Piano_pedal_engraver",
  "Piano_pedal_performer",
  "Pitch_squash_engraver",
  "Pitched_trill_engraver",
  "Pure_from_neighbor_engraver",
  "Repeat_acknowledge_engraver",
  "Repeat_tie_engraver",
  "Rest_collision_engraver",
  "Rest_engraver",
  "Rhythmic_column_engraver",
  "Script_column_engraver",
  "Script_engraver",
  "Script_row_engraver",
  "Separating_line_group_engraver",
  "Show_control_points_engraver",
  "Signum_repetitionis_engraver",
  "Skip_typesetting_engraver",
  "Slash_repeat_engraver",
  "Slur_engraver",
  "Slur_performer",
  "Spacing_engraver",
  "Span_arpeggio_engraver",
  "Span_bar_engraver",
  "Span_bar_stub_engraver",
  "Span_stem_engraver",
  "Spanner_break_forbid_engraver",
  "Spanner_tracking_engraver",
  "Staff_collecting_engraver",
  "Staff_highlight_engraver",
  "Staff_performer",
  "Staff_symbol_engraver",
  "Stanza_number_align_engraver",
  "Stanza_number_engraver",
  "Stem_engraver",
  "System_start_delimiter_engraver",
  "Tab_note_heads_engraver",
  "Tab_staff_symbol_engraver",
  "Tab_tie_follow_engraver",
  "Tempo_performer",
  "Text_engraver",
  "Text_mark_engraver",
  "Text_spanner_engraver",
  "Tie_engraver",
  "Tie_performer",
  "Time_signature_engraver",
  "Time_signature_performer",
  "Timing_translator",
  "Trill_spanner_engraver",
  "Tuplet_engraver",
  "Tweak_engraver",
  "Vaticana_ligature_engraver",
  "Vertical_align_engraver",
  "Volta_engraver",
]

scheme_functions = [
  "!=",
  "*location*",
  "*parser*",
  "Alteration_glyph_engraver",
  "Beat_performer",
  "Bend_spanner_engraver",
  "Breathing_sign_engraver",
  "Centered_bar_number_align_engraver",
  "Chord_name_engraver",
  "Chord_square_engraver",
  "Current_chord_text_engraver",
  "Divisio_engraver",
  "Duration_line_engraver",
  "Finger_glide_engraver",
  "G_",
  "Grid_chord_name_engraver",
  "Lyric_repeat_count_engraver",
  "Measure_counter_engraver",
  "Measure_spanner_engraver",
  "Merge_mmrest_numbers_engraver",
  "Merge_rests_engraver",
  "Show_control_points_engraver",
  "Signum_repetitionis_engraver",
  "Skip_typesetting_engraver",
  "Span_stem_engraver",
  "Spanner_tracking_engraver",
  "Staff_highlight_engraver",
  "Text_mark_engraver",
  "Trill_spanner_engraver",
  "_i",
  "abs-fontsize-markup",
  "accidental->markup",
  "accidental->markup-italian",
  "accidental-interface::calc-alteration",
  "accidental-interface::calc-glyph-name",
  "accidental-invalid?",
  "accidental-markup",
  "add-bar-glyph-print-procedure",
  "add-font",
  "add-grace-property",
  "add-music",
  "add-music-fonts",
  "add-new-clef",
  "add-pango-fonts",
  "add-point",
  "add-quotable",
  "add-score",
  "add-simple-time-signature-style",
  "add-stroke-glyph",
  "add-stroke-straight",
  "add-text",
  "adjust-slash-stencil",
  "align-on-other-markup",
  "aligned-text-stencil-function",
  "alist->hash-table",
  "alist<?",
  "alist?",
  "all-bar-numbers-visible",
  "all-equal?",
  "all-repeat-counts-visible",
  "allow-volta-hook",
  "alteration->text-accidental-markup",
  "alterations-in-key",
  "ambitus-line::calc-gap",
  "ambitus::print",
  "analyse-spanner-states",
  "ancestor-lookup-initialize",
  "angle-0-2pi",
  "angle-0-360",
  "annotate-spacing-spec",
  "annotate-y-interval",
  "any-mmrest-events",
  "apply-durations",
  "apply-group-draw-rule-series",
  "arrow-head-markup",
  "arrow-stencil",
  "arrow-stencil-maker",
  "assemble-stencils",
  "assoc-get",
  "assoc-keys",
  "assoc-values",
  "at-bar-line-substitute-caesura-type",
  "aug-modifier",
  "auto-footnote-markup",
  "average",
  "b",
  "backslashed-digit-markup",
  "bar-line::bar-y-extent",
  "bar-line::calc-blot",
  "bar-line::calc-break-visibility",
  "bar-line::calc-glyph-name",
  "bar-line::calc-glyph-name-for-direction",
  "bar-line::compound-bar-line",
  "bar-line::draw-filled-box",
  "bar-line::widen-bar-extent-on-span",
  "base-length",
  "bass-clarinet-rh-ees-key-stencil",
  "bassoon-bend-info-maker",
  "bassoon-cc-six-key-stencil",
  "bassoon-lh-a-flick-key-stencil",
  "bassoon-lh-c-flick-key-stencil",
  "bassoon-lh-cis-key-stencil",
  "bassoon-lh-d-flick-key-stencil",
  "bassoon-lh-ees-key-stencil",
  "bassoon-lh-he-key-stencil",
  "bassoon-lh-hees-key-stencil",
  "bassoon-lh-lb-key-stencil",
  "bassoon-lh-lbes-key-stencil",
  "bassoon-lh-lc-key-stencil",
  "bassoon-lh-ld-key-stencil",
  "bassoon-lh-lhees-key-stencil",
  "bassoon-lh-thumb-cis-key-stencil",
  "bassoon-lh-whisper-key-stencil",
  "bassoon-midline-rule",
  "bassoon-rh-bes-key-stencil",
  "bassoon-rh-cis-key-stencil",
  "bassoon-rh-f-key-stencil",
  "bassoon-rh-fis-key-stencil",
  "bassoon-rh-gis-key-stencil",
  "bassoon-rh-thumb-bes-key-stencil",
  "bassoon-rh-thumb-e-key-stencil",
  "bassoon-rh-thumb-fis-key-stencil",
  "bassoon-rh-thumb-gis-key-stencil",
  "bassoon-uber-key-stencil",
  "beam-exceptions",
  "beam-markup",
  "beam::align-with-broken-parts",
  "beam::get-kievan-positions",
  "beam::get-kievan-quantized-positions",
  "beam::place-broken-parts-individually",
  "beam::slope-like-broken-parts",
  "beat-grouping-internal",
  "beat-structure",
  "bend-spanner::print",
  "bend::arrow-head-stencil",
  "bend::calc-bend-x-begin",
  "bend::calc-bend-x-end",
  "bend::calc-y-coordinates",
  "bend::draw-curves",
  "bend::make-line-curve-stencil",
  "bend::print",
  "bend::remove-certain-tab-note-heads",
  "bend::target-cautionary",
  "bend::text-stencil",
  "bend::text-string",
  "bezier-head-for-stencil",
  "binary-search",
  "bold-markup",
  "book-first-page",
  "boolean-or-number?",
  "boolean-or-symbol?",
  "bounding-note-heads-pitches",
  "box-grob-stencil",
  "box-markup",
  "box-stencil",
  "bracket-markup",
  "bracketify-stencil",
  "break-alignable-interface::self-alignment-of-anchor",
  "break-alignable-interface::self-alignment-opposite-of-anchor",
  "break-alignment-list",
  "breathe::midi-length",
  "buildflag",
  "cached-file-contents",
  "caesura-script-interface::before-line-breaking",
  "caesura-to-bar-line-or-divisio",
  "caesura-to-divisio",
  "calc-harmonic-pitch",
  "calc-line-thickness",
  "calc-repeat-slash-count",
  "calculate-complex-compound-time",
  "calculate-compound-base-beat",
  "calculate-compound-base-beat-full",
  "calculate-compound-beat-grouping",
  "calculate-compound-measure-length",
  "calculate-time-fraction",
  "call-after-session",
  "caps-markup",
  "car-or-identity",
  "car<",
  "car<=",
  "cdr-or-identity",
  "center-align-markup",
  "center-column-markup",
  "centered-spanner-interface::calc-x-offset",
  "centered-stencil",
  "chain-assoc-get",
  "change-pitches",
  "char-markup",
  "cheap-list?",
  "cheap-markup?",
  "check-beam-quant",
  "check-beam-slope-sign",
  "check-broken-spanner",
  "check-context-path",
  "check-division-alist",
  "check-for-annotation",
  "check-for-replacement",
  "check-grob-path",
  "check-music-path",
  "check-pitch-against-signature",
  "check-quant-callbacks",
  "check-slope-callbacks",
  "chord-name->german-markup",
  "chord-name->italian-markup",
  "chord-square::height",
  "chord-square::print",
  "chord-square::width",
  "circle-markup",
  "circle-stencil",
  "clarinet-lh-R-key-stencil",
  "clarinet-lh-a-key-stencil",
  "clarinet-lh-cis-key-stencil",
  "clarinet-lh-d-key-stencil",
  "clarinet-lh-e-key-stencil",
  "clarinet-lh-ees-key-stencil",
  "clarinet-lh-f-key-stencil",
  "clarinet-lh-fis-key-stencil",
  "clarinet-lh-gis-key-stencil",
  "clarinet-lh-thumb-key-stencil",
  "clarinet-rh-b-key-stencil",
  "clarinet-rh-d-key-stencil",
  "clarinet-rh-e-key-stencil",
  "clarinet-rh-f-key-stencil",
  "clarinet-rh-fis-key-stencil",
  "clarinet-rh-four-key-stencil",
  "clarinet-rh-gis-key-stencil",
  "clarinet-rh-low-c-key-stencil",
  "clarinet-rh-low-cis-key-stencil",
  "clarinet-rh-low-d-key-stencil",
  "clarinet-rh-one-key-stencil",
  "clarinet-rh-three-key-stencil",
  "clarinet-rh-two-key-stencil",
  "clef-transposition-markup",
  "clef::print-modern-tab-if-set",
  "clip-systems-to-region-stencils",
  "clipped-systems-stencils",
  "close-enough?",
  "close-port-rename",
  "coda-markup",
  "collect-book-music-for-book",
  "collect-bookpart-for-book",
  "collect-music-aux",
  "collect-music-for-book",
  "collect-scores-for-book",
  "color?",
  "column-circle-stencil",
  "column-lines-markup-list",
  "column-markup",
  "combine-markup",
  "comparable-note-events",
  "comparator-from-key",
  "compile-all-markup-args",
  "compile-all-markup-expressions",
  "compile-markup-arg",
  "compile-markup-expression",
  "completize-formats",
  "completize-grob-entry",
  "compound-meter-markup",
  "concat-markup",
  "conditional-kern-before",
  "conditional-string-capitalize",
  "conditional-trill-markup-markup",
  "configuration",
  "cons-fret",
  "constante-hairpin",
  "construct-chord-elements",
  "context-defs-from-music",
  "context-mod-from-music",
  "context-spec-music",
  "control-point::calc-offset",
  "control-polygon::calc-text",
  "coord-axis",
  "coord-rotate",
  "coord-rotated",
  "coord-scale",
  "coord-translate",
  "coord-x",
  "coord-y",
  "copy-binary-file",
  "copy-repeat-chord",
  "count-list",
  "create-file-exclusive",
  "create-fretboard",
  "create-glyph-flag",
  "cross-staff-connect",
  "css-color",
  "cue-substitute",
  "current-or-previous-voice-states",
  "customTabClef-markup",
  "cyclic-base-value",
  "debugf",
  "def-grace-function",
  "default-auto-beam-check",
  "default-flag",
  "default-paren-color",
  "define-bar-line",
  "define-event-class",
  "define-event-function",
  "define-fonts",
  "define-grob-property",
  "define-internal-grob-property",
  "define-markup-command",
  "define-markup-command-internal",
  "define-markup-list-command",
  "define-music-function",
  "define-scheme-function",
  "define-session",
  "define-session-public",
  "define-syntax-function",
  "define-syntax-public",
  "define-syntax-rule-public",
  "define-tag-group",
  "define-void-function",
  "degree-first-true",
  "degrees->radians",
  "descend-to-context",
  "determine-frets",
  "determine-split-list",
  "determine-string-fret-finger",
  "dim-modifier",
  "dimension-arrows",
  "dir-basename",
  "dir-column-markup",
  "display-lily-music",
  "display-music",
  "display-scheme-music",
  "dodecaphonic-no-repeat-rule",
  "done?",
  "dot-column-interface::pad-by-one-dot-width",
  "dot-has-color",
  "dot-is-inverted",
  "dot-is-parenthesized",
  "dots::calc-dot-count",
  "dots::calc-dot-stencil",
  "dots::calc-glyph-name",
  "dots::calc-staff-position",
  "doubleflat-markup",
  "doublesharp-markup",
  "draw-circle-markup",
  "draw-dashed-line-markup",
  "draw-dotted-line-markup",
  "draw-hline-markup",
  "draw-line-markup",
  "draw-squiggle-line-markup",
  "dump-zombies",
  "duration",
  "duration-dot-factor",
  "duration-length",
  "duration-line::calc",
  "duration-line::calc-thickness",
  "duration-line::print",
  "duration-log-factor",
  "duration-of-note",
  "duration-or-music?",
  "duration-visual",
  "duration-visual-length",
  "dynamic-markup",
  "dynamic-text-spanner::before-line-breaking",
  "elbowed-hairpin",
  "ellipse-markup",
  "ellipse-radius",
  "ellipse-stencil",
  "empty-music",
  "end-broken-spanner?",
  "entry-greater-than-x?",
  "eps-file->stencil",
  "epsfile-markup",
  "eval-carefully",
  "event-cause",
  "event-chord-notes",
  "event-chord-pitches",
  "event-chord-reduce",
  "event-chord-wrap!",
  "event-class-cons",
  "event-has-articulation?",
  "events",
  "every-nth-bar-number-visible",
  "every-nth-repeat-count-visible",
  "exact-rational?",
  "expand-repeat-chords!",
  "expand-repeat-notes!",
  "extent-combine",
  "extract-alteration",
  "extract-beam-exceptions",
  "extract-music",
  "extract-named-music",
  "extract-typed-music",
  "eyeglasses-markup",
  "fermata-markup",
  "figured-bass-markup",
  "fill-line-markup",
  "fill-with-pattern-markup",
  "filled-box-markup",
  "find-named-props",
  "find-pitch-entry",
  "find-value-to-offset",
  "finger-glide::print",
  "finger-markup",
  "fingering::calc-text",
  "first-assoc",
  "first-bar-number-invisible",
  "first-bar-number-invisible-and-no-parenthesized-bar-numbers",
  "first-bar-number-invisible-save-broken-bars",
  "first-broken-spanner?",
  "first-member",
  "first-visible-markup",
  "flared-hairpin",
  "flat-flag",
  "flat-markup",
  "flatten-alist",
  "flatten-list",
  "flip-stencil",
  "flute-lh-b-key-stencil",
  "flute-lh-bes-key-stencil",
  "flute-lh-gis-key-stencil",
  "flute-lh-gis-rh-bes-key-stencil",
  "flute-rh-b-key-stencil",
  "flute-rh-bes-key-stencil",
  "flute-rh-c-key-stencil",
  "flute-rh-cis-key-stencil",
  "flute-rh-d-key-stencil",
  "flute-rh-dis-key-stencil",
  "flute-rh-ees-key-stencil",
  "flute-rh-gz-key-stencil",
  "fold-some-music",
  "font-children",
  "font-default",
  "font-name-split",
  "font-name-style",
  "font-qualifier",
  "fontCaps-markup",
  "fontsize-markup",
  "footnote-markup",
  "for-some-music",
  "forced-configuration",
  "format",
  "format-bass-figure",
  "format-coda-mark",
  "format-compound-time",
  "format-dal-segno-text",
  "format-dal-segno-text-brief",
  "format-mark-alphabet",
  "format-mark-barnumbers",
  "format-mark-box-alphabet",
  "format-mark-box-barnumbers",
  "format-mark-box-letters",
  "format-mark-box-numbers",
  "format-mark-circle-alphabet",
  "format-mark-circle-barnumbers",
  "format-mark-circle-letters",
  "format-mark-circle-numbers",
  "format-mark-generic",
  "format-mark-letters",
  "format-mark-numbers",
  "format-metronome-markup",
  "format-segno-mark",
  "format-segno-mark-considering-bar-lines",
  "format-sign-with-number",
  "format-time-element",
  "format-time-fraction",
  "format-time-list",
  "format-time-numerator",
  "format-varcoda-mark",
  "fraction->moment",
  "fraction-markup",
  "fraction?",
  "fret->pitch",
  "fret-board::calc-stencil",
  "fret-count",
  "fret-diagram-markup",
  "fret-diagram-terse-markup",
  "fret-diagram-verbose-markup",
  "fret-letter-tablature-format",
  "fret-number-tablature-format",
  "fret-number-tablature-format-banjo",
  "fret-parse-definition-string",
  "fret-parse-marking-list",
  "fret-parse-terse-definition-string",
  "fromproperty-markup",
  "function-chain",
  "g",
  "g-lookup-font",
  "general-align-markup",
  "general-column",
  "generate-bassoon-family-entry",
  "generate-clarinet-family-entry",
  "generate-crop-stencil",
  "generate-flute-family-entry",
  "generate-oboe-family-entry",
  "generate-preview-stencil",
  "generate-saxophone-family-entry",
  "generate-system-stencils",
  "generate-tin-whistle-family-entry",
  "get-bound-note-heads",
  "get-chord-shape",
  "get-current-filename",
  "get-current-suffix",
  "get-fill-space",
  "get-key",
  "get-named-spreadsheet-column",
  "get-next-unique-voice-name",
  "get-numeric-from-key",
  "get-outfile-name",
  "get-postscript-bbox",
  "get-quarter-diffs",
  "get-setting",
  "get-slope-offset",
  "get-span-glyph",
  "get-spreadsheet-column",
  "get-step",
  "get-sub-list",
  "get-top-most-tab-head",
  "get-tweakable-music",
  "get-woodwind-key-list",
  "glissando::calc-tab-extra-dy",
  "glissando::draw-tab-glissando",
  "glyph->stencil",
  "glyph-flag",
  "grace-spacing::calc-shortest-duration",
  "gray-colorize",
  "grid-chord-name::calc-X-offset",
  "grid-chord-name::calc-Y-offset",
  "grid-chord-name::calc-offset-on-axis",
  "grob-interpret-markup",
  "grob-list?",
  "grob-transformer",
  "grob::all-objects",
  "grob::calc-property-by-copy",
  "grob::compose-function",
  "grob::display-objects",
  "grob::has-interface",
  "grob::inherit-parent-property",
  "grob::is-live?",
  "grob::name",
  "grob::objects-from-interface",
  "grob::offset-function",
  "grob::relay-other-property",
  "grob::rhythmic-location",
  "grob::show-skylines-if-debug-skylines-set",
  "grob::unpure-Y-extent-from-stencil",
  "grob::when",
  "group-automate-rule",
  "group-draw-rule",
  "group-extra-offset-rule",
  "gs-cmd-args",
  "gs-safe-run",
  "hairpin::calc-grow-direction",
  "halign-markup",
  "harp-pedal-check",
  "harp-pedal-info",
  "harp-pedal-markup",
  "harp-pedals-parse-string",
  "has-at-least-two?",
  "has-one-or-less?",
  "hash-table->alist",
  "hbracket-markup",
  "hcenter-in-markup",
  "header-to-file",
  "headers-property-alist-chain",
  "hook-stencil",
  "horizontal-slash-interval",
  "hspace-markup",
  "huge-markup",
  "if-markup",
  "ignatzek-chord-names",
  "index-cell",
  "index-or-markup?",
  "index?",
  "insert-markups",
  "internal-set-paper-size",
  "interpret-markup",
  "interpret-markup-list",
  "interval-bound",
  "interval-center",
  "interval-contains?",
  "interval-empty?",
  "interval-end",
  "interval-index",
  "interval-intersection",
  "interval-length",
  "interval-sane?",
  "interval-scale",
  "interval-start",
  "interval-union",
  "interval-widen",
  "invalidate-alterations",
  "inverter-factory",
  "is-absolute?",
  "is-square?",
  "italic-markup",
  "item::extra-spacing-height-including-staff",
  "justified-lines-markup-list",
  "justify-field-markup",
  "justify-line-helper",
  "justify-line-markup",
  "justify-markup",
  "justify-string-markup",
  "key-crawler",
  "key-entry-alteration",
  "key-entry-bar-number",
  "key-entry-end-mom",
  "key-entry-notename",
  "key-entry-octave",
  "key-fill-translate",
  "key-list-or-music?",
  "key-list-or-symbol?",
  "key-list?",
  "key-signature-interface::alteration-position",
  "key-signature-interface::alteration-positions",
  "key?",
  "keyword->make-markup",
  "large-markup",
  "larger-markup",
  "layout-blot-diameter",
  "layout-extract-page-properties",
  "layout-line-thickness",
  "layout-set-absolute-staff-size",
  "layout-set-absolute-staff-size-in-module",
  "layout-set-staff-size",
  "left-align-markup",
  "left-brace-markup",
  "left-column-markup",
  "lexicographic-list-compare?",
  "lh-woodwind-text-stencil",
  "lilypond-all",
  "lilypond-file",
  "lilypond-main",
  "lilypond-version",
  "lilypond-version-outdated?",
  "line-markup",
  "list-all-possible-keys",
  "list-all-possible-keys-verbose",
  "list-element-index",
  "list-insert-separator",
  "list-join",
  "listener->once-listener",
  "little-elliptical-key-stencil",
  "long-midline-stencil",
  "lookup-font",
  "lookup-markup",
  "lookup-markup-command",
  "lookup-markup-command-aux",
  "lookup-markup-list-command",
  "lookup-paper-name",
  "low-bass-clarinet-rh-ees-key-stencil",
  "lower-markup",
  "ly-getcwd",
  "ly-type?",
  "ly:accidental-interface::height",
  "ly:accidental-interface::horizontal-skylines",
  "ly:accidental-interface::print",
  "ly:accidental-interface::remove-tied",
  "ly:accidental-placement::calc-positioning-done",
  "ly:add-context-mod",
  "ly:add-interface",
  "ly:add-listener",
  "ly:add-option",
  "ly:align-interface::align-to-ideal-distances",
  "ly:align-interface::align-to-minimum-distances",
  "ly:all-grob-interfaces",
  "ly:all-options",
  "ly:all-output-backend-commands",
  "ly:all-stencil-commands",
  "ly:all-stencil-expressions",
  "ly:alternative-sequence-iterator::constructor",
  "ly:angle",
  "ly:apply-context-iterator::constructor",
  "ly:arpeggio::brew-chord-bracket",
  "ly:arpeggio::brew-chord-slur",
  "ly:arpeggio::calc-cross-staff",
  "ly:arpeggio::calc-positions",
  "ly:arpeggio::print",
  "ly:arpeggio::pure-height",
  "ly:arpeggio::width",
  "ly:assoc-get",
  "ly:axis-group-interface::add-element",
  "ly:axis-group-interface::adjacent-pure-heights",
  "ly:axis-group-interface::calc-pure-relevant-grobs",
  "ly:axis-group-interface::calc-pure-staff-staff-spacing",
  "ly:axis-group-interface::calc-pure-y-common",
  "ly:axis-group-interface::calc-skylines",
  "ly:axis-group-interface::calc-staff-staff-spacing",
  "ly:axis-group-interface::calc-x-common",
  "ly:axis-group-interface::calc-y-common",
  "ly:axis-group-interface::combine-skylines",
  "ly:axis-group-interface::height",
  "ly:axis-group-interface::pure-height",
  "ly:axis-group-interface::width",
  "ly:balloon-interface::print",
  "ly:balloon-interface::pure-height",
  "ly:balloon-interface::remove-irrelevant-spanner",
  "ly:balloon-interface::width",
  "ly:bar-check-iterator::constructor",
  "ly:bar-line::calc-anchor",
  "ly:bar-line::calc-bar-extent",
  "ly:bar-line::print",
  "ly:basic-progress",
  "ly:beam::calc-beam-segments",
  "ly:beam::calc-beaming",
  "ly:beam::calc-cross-staff",
  "ly:beam::calc-direction",
  "ly:beam::calc-normal-stems",
  "ly:beam::calc-stem-shorten",
  "ly:beam::calc-x-positions",
  "ly:beam::print",
  "ly:beam::pure-rest-collision-callback",
  "ly:beam::quanting",
  "ly:beam::rest-collision-callback",
  "ly:beam::set-stem-lengths",
  "ly:bezier-extent",
  "ly:bezier-extract",
  "ly:book-add-bookpart!",
  "ly:book-add-score!",
  "ly:book-book-parts",
  "ly:book-header",
  "ly:book-paper",
  "ly:book-process",
  "ly:book-process-to-systems",
  "ly:book-scores",
  "ly:book-set-header!",
  "ly:book?",
  "ly:bp",
  "ly:bracket",
  "ly:break-alignable-interface::find-parent",
  "ly:break-alignable-interface::self-align-callback",
  "ly:break-aligned-interface::calc-average-anchor",
  "ly:break-aligned-interface::calc-break-visibility",
  "ly:break-aligned-interface::calc-extent-aligned-anchor",
  "ly:break-aligned-interface::calc-joint-anchor-alignment",
  "ly:break-alignment-interface::calc-positioning-done",
  "ly:break-alignment-interface::find-nonempty-break-align-group",
  "ly:breathing-sign::divisio-maior",
  "ly:breathing-sign::divisio-maxima",
  "ly:breathing-sign::divisio-minima",
  "ly:breathing-sign::finalis",
  "ly:breathing-sign::offset-callback",
  "ly:breathing-sign::set-breath-properties",
  "ly:broadcast",
  "ly:cairo-output-stencil",
  "ly:cairo-output-stencils",
  "ly:calculated-sequential-music::length",
  "ly:calculated-sequential-music::start",
  "ly:camel-case->lisp-identifier",
  "ly:chain-assoc-get",
  "ly:change-iterator::constructor",
  "ly:check-expected-warnings",
  "ly:chord-name::after-line-breaking",
  "ly:clef-modifier::calc-parent-alignment",
  "ly:clef::calc-glyph-name",
  "ly:clef::print",
  "ly:cluster-beacon::height",
  "ly:cluster::calc-cross-staff",
  "ly:cluster::print",
  "ly:cm",
  "ly:command-line-code",
  "ly:command-line-options",
  "ly:connect-dispatchers",
  "ly:context-current-moment",
  "ly:context-def-lookup",
  "ly:context-def-modify",
  "ly:context-def?",
  "ly:context-event-source",
  "ly:context-events-below",
  "ly:context-find",
  "ly:context-grob-definition",
  "ly:context-id",
  "ly:context-matched-pop-property",
  "ly:context-mod-apply!",
  "ly:context-mod?",
  "ly:context-name",
  "ly:context-output-def",
  "ly:context-parent",
  "ly:context-property",
  "ly:context-property-where-defined",
  "ly:context-pushpop-property",
  "ly:context-set-property!",
  "ly:context-specced-music-iterator::constructor",
  "ly:context-unset-property",
  "ly:context?",
  "ly:custos::print",
  "ly:debug",
  "ly:default-scale",
  "ly:dimension?",
  "ly:dir?",
  "ly:directed",
  "ly:disconnect-dispatchers",
  "ly:dispatcher?",
  "ly:dot-column::calc-positioning-done",
  "ly:dots::print",
  "ly:duration->string",
  "ly:duration-compress",
  "ly:duration-dot-count",
  "ly:duration-factor",
  "ly:duration-length",
  "ly:duration-log",
  "ly:duration-scale",
  "ly:duration::less?",
  "ly:duration<?",
  "ly:duration?",
  "ly:effective-prefix",
  "ly:enclosing-bracket::print",
  "ly:enclosing-bracket::width",
  "ly:engraver-announce-end-grob",
  "ly:engraver-make-grob",
  "ly:engraver-make-item",
  "ly:engraver-make-spanner",
  "ly:engraver-make-sticky",
  "ly:error",
  "ly:event-chord-iterator::constructor",
  "ly:event-deep-copy",
  "ly:event-iterator::constructor",
  "ly:event-property",
  "ly:event-set-property!",
  "ly:event-warning",
  "ly:event?",
  "ly:exit",
  "ly:expect-warning",
  "ly:extract-subfont-from-collection",
  "ly:figured-bass-continuation::center-on-figures",
  "ly:figured-bass-continuation::print",
  "ly:find-file",
  "ly:fine-iterator::constructor",
  "ly:fingering-column::calc-positioning-done",
  "ly:flag::calc-x-offset",
  "ly:flag::calc-y-offset",
  "ly:flag::glyph-name",
  "ly:flag::print",
  "ly:flag::pure-calc-y-offset",
  "ly:flag::width",
  "ly:font-config-add-directory",
  "ly:font-config-add-font",
  "ly:font-config-display-fonts",
  "ly:font-config-get-font-file",
  "ly:font-design-size",
  "ly:font-file-name",
  "ly:font-get-glyph",
  "ly:font-glyph-name-to-charcode",
  "ly:font-glyph-name-to-index",
  "ly:font-index-to-charcode",
  "ly:font-magnification",
  "ly:font-metric?",
  "ly:font-name",
  "ly:font-sub-fonts",
  "ly:format",
  "ly:format-output",
  "ly:generic-bound-extent",
  "ly:get-all-function-documentation",
  "ly:get-all-translators",
  "ly:get-cff-offset",
  "ly:get-context-mods",
  "ly:get-font-format",
  "ly:get-option",
  "ly:get-spacing-spec",
  "ly:grace-iterator::constructor",
  "ly:grace-music::start-callback",
  "ly:grid-line-interface::print",
  "ly:grid-line-interface::width",
  "ly:grob-alist-chain",
  "ly:grob-array->list",
  "ly:grob-array-length",
  "ly:grob-array-ref",
  "ly:grob-array?",
  "ly:grob-basic-properties",
  "ly:grob-chain-callback",
  "ly:grob-common-refpoint",
  "ly:grob-common-refpoint-of-array",
  "ly:grob-default-font",
  "ly:grob-extent",
  "ly:grob-get-vertical-axis-group-index",
  "ly:grob-interfaces",
  "ly:grob-layout",
  "ly:grob-list->grob-array",
  "ly:grob-object",
  "ly:grob-original",
  "ly:grob-parent",
  "ly:grob-pq<?",
  "ly:grob-properties?",
  "ly:grob-property",
  "ly:grob-property-data",
  "ly:grob-pure-height",
  "ly:grob-pure-property",
  "ly:grob-relative-coordinate",
  "ly:grob-robust-relative-extent",
  "ly:grob-script-priority-less",
  "ly:grob-set-nested-property!",
  "ly:grob-set-object!",
  "ly:grob-set-parent!",
  "ly:grob-set-property!",
  "ly:grob-spanned-column-rank-interval",
  "ly:grob-staff-position",
  "ly:grob-suicide!",
  "ly:grob-system",
  "ly:grob-translate-axis!",
  "ly:grob-vertical<?",
  "ly:grob-warning",
  "ly:grob::horizontal-skylines-from-element-stencils",
  "ly:grob::horizontal-skylines-from-stencil",
  "ly:grob::pure-horizontal-skylines-from-element-stencils",
  "ly:grob::pure-simple-horizontal-skylines-from-extents",
  "ly:grob::pure-simple-vertical-skylines-from-extents",
  "ly:grob::pure-stencil-height",
  "ly:grob::pure-vertical-skylines-from-element-stencils",
  "ly:grob::simple-horizontal-skylines-from-extents",
  "ly:grob::simple-vertical-skylines-from-extents",
  "ly:grob::stencil-height",
  "ly:grob::stencil-width",
  "ly:grob::vertical-skylines-from-element-stencils",
  "ly:grob::vertical-skylines-from-stencil",
  "ly:grob::x-parent-positioning",
  "ly:grob::y-parent-positioning",
  "ly:grob?",
  "ly:gs-cli",
  "ly:gulp-file",
  "ly:gulp-file-utf8",
  "ly:hairpin::broken-bound-padding",
  "ly:hairpin::print",
  "ly:hairpin::pure-height",
  "ly:hara-kiri-group-spanner::calc-skylines",
  "ly:hara-kiri-group-spanner::force-hara-kiri-callback",
  "ly:hara-kiri-group-spanner::force-hara-kiri-in-y-parent-callback",
  "ly:hara-kiri-group-spanner::pure-height",
  "ly:hara-kiri-group-spanner::y-extent",
  "ly:has-glyph-names?",
  "ly:hash-table-keys",
  "ly:horizontal-bracket-text::calc-direction",
  "ly:horizontal-bracket-text::print",
  "ly:horizontal-bracket::print",
  "ly:horizontal-line-spanner::calc-left-bound-info",
  "ly:horizontal-line-spanner::calc-left-bound-info-and-text",
  "ly:horizontal-line-spanner::calc-right-bound-info",
  "ly:in-event-class?",
  "ly:inch",
  "ly:input-both-locations",
  "ly:input-file-line-char-column",
  "ly:input-location?",
  "ly:input-message",
  "ly:input-warning",
  "ly:interpret-music-expression",
  "ly:intlog2",
  "ly:item-break-dir",
  "ly:item-get-column",
  "ly:item?",
  "ly:iterator?",
  "ly:key-signature-interface::print",
  "ly:kievan-ligature::print",
  "ly:ledger-line-spanner::print",
  "ly:ledger-line-spanner::set-spacing-rods",
  "ly:length",
  "ly:lily-lexer?",
  "ly:lily-parser?",
  "ly:line-interface::line",
  "ly:line-spanner::calc-cross-staff",
  "ly:line-spanner::calc-left-bound-info",
  "ly:line-spanner::calc-left-bound-info-and-text",
  "ly:line-spanner::calc-right-bound-info",
  "ly:line-spanner::print",
  "ly:list->offsets",
  "ly:listened-event-class?",
  "ly:listened-event-types",
  "ly:listener?",
  "ly:load",
  "ly:lyric-combine-music-iterator::constructor",
  "ly:lyric-combine-music::length-callback",
  "ly:lyric-extender::print",
  "ly:lyric-hyphen::print",
  "ly:lyric-hyphen::set-spacing-rods",
  "ly:make-book",
  "ly:make-book-part",
  "ly:make-context-mod",
  "ly:make-dispatcher",
  "ly:make-duration",
  "ly:make-event-class",
  "ly:make-global-context",
  "ly:make-global-translator",
  "ly:make-grob-properties",
  "ly:make-listener",
  "ly:make-moment",
  "ly:make-music",
  "ly:make-music-function",
  "ly:make-music-relative!",
  "ly:make-output-def",
  "ly:make-page-label-marker",
  "ly:make-page-permission-marker",
  "ly:make-pango-description-string",
  "ly:make-paper-outputter",
  "ly:make-pitch",
  "ly:make-prob",
  "ly:make-rotation",
  "ly:make-scale",
  "ly:make-scaling",
  "ly:make-score",
  "ly:make-skyline",
  "ly:make-spring",
  "ly:make-stencil",
  "ly:make-stream-event",
  "ly:make-transform",
  "ly:make-translation",
  "ly:make-unpure-pure-container",
  "ly:measure-grouping::print",
  "ly:measure-spanner::calc-connect-to-neighbors",
  "ly:measure-spanner::print",
  "ly:melody-spanner::calc-neutral-stem-direction",
  "ly:mensural-ligature::brew-ligature-primitive",
  "ly:mensural-ligature::print",
  "ly:message",
  "ly:minimal-breaking",
  "ly:mm",
  "ly:module->alist",
  "ly:module-copy",
  "ly:modules-lookup",
  "ly:moment-add",
  "ly:moment-div",
  "ly:moment-grace",
  "ly:moment-grace-denominator",
  "ly:moment-grace-numerator",
  "ly:moment-main",
  "ly:moment-main-denominator",
  "ly:moment-main-numerator",
  "ly:moment-mod",
  "ly:moment-mul",
  "ly:moment-sub",
  "ly:moment<?",
  "ly:moment?",
  "ly:multi-measure-rest::height",
  "ly:multi-measure-rest::print",
  "ly:multi-measure-rest::set-spacing-rods",
  "ly:multi-measure-rest::set-text-rods",
  "ly:music-compress",
  "ly:music-deep-copy",
  "ly:music-duration-compress",
  "ly:music-duration-length",
  "ly:music-error",
  "ly:music-function-extract",
  "ly:music-function-signature",
  "ly:music-function?",
  "ly:music-iterator::constructor",
  "ly:music-length",
  "ly:music-list?",
  "ly:music-message",
  "ly:music-mutable-properties",
  "ly:music-output?",
  "ly:music-property",
  "ly:music-sequence::cumulative-length-callback",
  "ly:music-sequence::event-chord-length-callback",
  "ly:music-sequence::event-chord-relative-callback",
  "ly:music-sequence::first-start-callback",
  "ly:music-sequence::maximum-length-callback",
  "ly:music-sequence::minimum-start-callback",
  "ly:music-sequence::simultaneous-relative-callback",
  "ly:music-set-property!",
  "ly:music-start",
  "ly:music-transpose",
  "ly:music-warning",
  "ly:music-wrapper-iterator::constructor",
  "ly:music-wrapper::length-callback",
  "ly:music-wrapper::start-callback",
  "ly:music::duration-length-callback",
  "ly:music?",
  "ly:non-fatal-error",
  "ly:note-collision-interface::calc-positioning-done",
  "ly:note-column-accidentals",
  "ly:note-column-dot-column",
  "ly:note-column::calc-main-extent",
  "ly:note-extra-source-file",
  "ly:note-head::calc-stem-attachment",
  "ly:note-head::calc-tab-stem-attachment",
  "ly:note-head::include-ledger-line-height",
  "ly:note-head::print",
  "ly:note-head::stem-attachment",
  "ly:note-head::stem-x-shift",
  "ly:number->string",
  "ly:number-pair->string",
  "ly:one-line-auto-height-breaking",
  "ly:one-line-breaking",
  "ly:one-page-breaking",
  "ly:optimal-breaking",
  "ly:option-usage",
  "ly:otf->cff",
  "ly:otf-font-glyph-info",
  "ly:otf-font-table-data",
  "ly:otf-font?",
  "ly:otf-glyph-count",
  "ly:otf-glyph-list",
  "ly:ottava-bracket::print",
  "ly:output-def-clone",
  "ly:output-def-lookup",
  "ly:output-def-parent",
  "ly:output-def-scope",
  "ly:output-def-set-variable!",
  "ly:output-def?",
  "ly:output-description",
  "ly:output-find-context-def",
  "ly:outputter-close",
  "ly:outputter-dump-stencil",
  "ly:outputter-dump-string",
  "ly:outputter-output-scheme",
  "ly:outputter-port",
  "ly:page-marker?",
  "ly:page-turn-breaking",
  "ly:pango-font-physical-fonts",
  "ly:pango-font?",
  "ly:paper-book-header",
  "ly:paper-book-pages",
  "ly:paper-book-paper",
  "ly:paper-book-performances",
  "ly:paper-book-scopes",
  "ly:paper-book-systems",
  "ly:paper-book?",
  "ly:paper-column::break-align-width",
  "ly:paper-column::print",
  "ly:paper-fonts",
  "ly:paper-get-font",
  "ly:paper-get-number",
  "ly:paper-outputscale",
  "ly:paper-score-paper-systems",
  "ly:paper-system-minimum-distance",
  "ly:paper-system?",
  "ly:parse-file",
  "ly:parse-init",
  "ly:parse-string-expression",
  "ly:parsed-undead-list!",
  "ly:parser-clear-error",
  "ly:parser-clone",
  "ly:parser-define!",
  "ly:parser-error",
  "ly:parser-has-error?",
  "ly:parser-include-string",
  "ly:parser-lookup",
  "ly:parser-output-name",
  "ly:parser-parse-string",
  "ly:parser-set-note-names",
  "ly:part-combine-iterator::constructor",
  "ly:partial-iterator::constructor",
  "ly:partial-iterator::finalization",
  "ly:percent-repeat-interface::beat-slash",
  "ly:percent-repeat-interface::double-percent",
  "ly:percent-repeat-interface::percent",
  "ly:percent-repeat-iterator::constructor",
  "ly:perform-text-replacements",
  "ly:performance-headers",
  "ly:performance-write",
  "ly:piano-pedal-bracket::print",
  "ly:pitch-alteration",
  "ly:pitch-diff",
  "ly:pitch-negate",
  "ly:pitch-notename",
  "ly:pitch-octave",
  "ly:pitch-quartertones",
  "ly:pitch-semitones",
  "ly:pitch-steps",
  "ly:pitch-tones",
  "ly:pitch-transpose",
  "ly:pitch::less?",
  "ly:pitch<?",
  "ly:pitch?",
  "ly:pointer-group-interface::add-grob",
  "ly:pop-property-iterator::constructor",
  "ly:position-on-line?",
  "ly:prob-immutable-properties",
  "ly:prob-mutable-properties",
  "ly:prob-property",
  "ly:prob-property?",
  "ly:prob-set-property!",
  "ly:prob-type?",
  "ly:prob?",
  "ly:programming-error",
  "ly:progress",
  "ly:property-iterator::constructor",
  "ly:property-lookup-stats",
  "ly:property-unset-iterator::constructor",
  "ly:pt",
  "ly:pure-call",
  "ly:pure-from-neighbor-interface::calc-pure-relevant-grobs",
  "ly:push-property-iterator::constructor",
  "ly:quote-iterator::constructor",
  "ly:randomize-rand-seed",
  "ly:register-stencil-expression",
  "ly:register-translator",
  "ly:relative-group-extent",
  "ly:relative-octave-check::relative-callback",
  "ly:relative-octave-music::no-relative-callback",
  "ly:relative-octave-music::relative-callback",
  "ly:rename-file",
  "ly:reset-all-fonts",
  "ly:rest-collision::calc-positioning-done",
  "ly:rest-collision::force-shift-callback-rest",
  "ly:rest::calc-cross-staff",
  "ly:rest::height",
  "ly:rest::print",
  "ly:rest::pure-height",
  "ly:rest::width",
  "ly:rest::y-offset-callback",
  "ly:rhythmic-music-iterator::constructor",
  "ly:round-filled-box",
  "ly:round-polygon",
  "ly:run-translator",
  "ly:score-add-output-def!",
  "ly:score-embedded-format",
  "ly:score-error?",
  "ly:score-header",
  "ly:score-music",
  "ly:score-output-defs",
  "ly:score-set-header!",
  "ly:score?",
  "ly:script-column::before-line-breaking",
  "ly:script-column::row-before-line-breaking",
  "ly:script-interface::calc-cross-staff",
  "ly:script-interface::calc-direction",
  "ly:script-interface::calc-positioning-done",
  "ly:script-interface::print",
  "ly:self-alignment-interface::aligned-on-x-parent",
  "ly:self-alignment-interface::aligned-on-y-parent",
  "ly:self-alignment-interface::centered-on-x-parent",
  "ly:self-alignment-interface::centered-on-y-parent",
  "ly:self-alignment-interface::pure-y-aligned-on-self",
  "ly:self-alignment-interface::x-aligned-on-self",
  "ly:self-alignment-interface::y-aligned-on-self",
  "ly:semi-tie-column::calc-head-direction",
  "ly:semi-tie-column::calc-positioning-done",
  "ly:semi-tie::calc-control-points",
  "ly:separation-item::calc-skylines",
  "ly:sequential-iterator::constructor",
  "ly:set-color-names",
  "ly:set-default-scale",
  "ly:set-grob-creation-callback",
  "ly:set-grob-modification-callback",
  "ly:set-middle-C!",
  "ly:set-option",
  "ly:set-origin!",
  "ly:set-property-cache-callback",
  "ly:side-position-interface::calc-cross-staff",
  "ly:side-position-interface::move-to-extremal-staff",
  "ly:side-position-interface::pure-y-aligned-side",
  "ly:side-position-interface::set-axis!",
  "ly:side-position-interface::x-aligned-side",
  "ly:side-position-interface::y-aligned-side",
  "ly:simple-music-iterator::constructor",
  "ly:simultaneous-music-iterator::constructor",
  "ly:skyline->points",
  "ly:skyline-distance",
  "ly:skyline-empty?",
  "ly:skyline-height",
  "ly:skyline-max-height",
  "ly:skyline-max-height-position",
  "ly:skyline-merge",
  "ly:skyline-pad",
  "ly:skyline-pair?",
  "ly:skyline-touching-point",
  "ly:skyline?",
  "ly:skylines-for-stencil",
  "ly:slur::calc-control-points",
  "ly:slur::calc-cross-staff",
  "ly:slur::calc-direction",
  "ly:slur::height",
  "ly:slur::outside-slur-callback",
  "ly:slur::outside-slur-cross-staff",
  "ly:slur::print",
  "ly:slur::pure-height",
  "ly:slur::pure-outside-slur-callback",
  "ly:smob-protects",
  "ly:solve-spring-rod-problem",
  "ly:source-file?",
  "ly:source-files",
  "ly:spacing-spanner::calc-common-shortest-duration",
  "ly:spacing-spanner::set-springs",
  "ly:span-bar::before-line-breaking",
  "ly:span-bar::calc-anchor",
  "ly:span-bar::calc-glyph-name",
  "ly:span-bar::choose-model-bar-line",
  "ly:span-bar::print",
  "ly:span-bar::width",
  "ly:spanner-bound",
  "ly:spanner-broken-into",
  "ly:spanner-set-bound!",
  "ly:spanner::bounds-width",
  "ly:spanner::calc-normalized-endpoints",
  "ly:spanner::kill-zero-spanned-time",
  "ly:spanner::set-spacing-rods",
  "ly:spanner?",
  "ly:spawn",
  "ly:spring-set-inverse-compress-strength!",
  "ly:spring-set-inverse-stretch-strength!",
  "ly:spring?",
  "ly:staff-symbol-line-thickness",
  "ly:staff-symbol-referencer::callback",
  "ly:staff-symbol-staff-radius",
  "ly:staff-symbol-staff-space",
  "ly:staff-symbol::height",
  "ly:staff-symbol::print",
  "ly:stderr-redirect",
  "ly:stem-tremolo::calc-cross-staff",
  "ly:stem-tremolo::calc-direction",
  "ly:stem-tremolo::calc-shape",
  "ly:stem-tremolo::calc-slope",
  "ly:stem-tremolo::calc-width",
  "ly:stem-tremolo::calc-y-offset",
  "ly:stem-tremolo::print",
  "ly:stem-tremolo::pure-calc-y-offset",
  "ly:stem-tremolo::pure-height",
  "ly:stem-tremolo::width",
  "ly:stem::calc-cross-staff",
  "ly:stem::calc-default-direction",
  "ly:stem::calc-direction",
  "ly:stem::calc-length",
  "ly:stem::calc-positioning-done",
  "ly:stem::calc-stem-begin-position",
  "ly:stem::calc-stem-end-position",
  "ly:stem::calc-stem-info",
  "ly:stem::height",
  "ly:stem::offset-callback",
  "ly:stem::print",
  "ly:stem::pure-calc-length",
  "ly:stem::pure-calc-stem-begin-position",
  "ly:stem::pure-calc-stem-end-position",
  "ly:stem::pure-height",
  "ly:stem::width",
  "ly:stencil-add",
  "ly:stencil-aligned-to",
  "ly:stencil-combine-at-edge",
  "ly:stencil-empty?",
  "ly:stencil-expr",
  "ly:stencil-extent",
  "ly:stencil-in-color",
  "ly:stencil-outline",
  "ly:stencil-rotate",
  "ly:stencil-rotate-absolute",
  "ly:stencil-scale",
  "ly:stencil-stack",
  "ly:stencil-translate",
  "ly:stencil-translate-axis",
  "ly:stencil?",
  "ly:stream-event?",
  "ly:string-percent-encode",
  "ly:string-substitute",
  "ly:sustain-pedal::print",
  "ly:system",
  "ly:system-font-load",
  "ly:system-start-delimiter::print",
  "ly:system::calc-pure-height",
  "ly:system::calc-pure-relevant-grobs",
  "ly:system::footnotes-after-line-breaking",
  "ly:system::footnotes-before-line-breaking",
  "ly:system::get-nonspaceable-staves",
  "ly:system::get-spaceable-staves",
  "ly:system::get-staves",
  "ly:system::get-vertical-alignment",
  "ly:system::height",
  "ly:system::vertical-skyline-elements",
  "ly:text-interface::interpret-markup",
  "ly:text-interface::interpret-string",
  "ly:text-interface::print",
  "ly:tie-column::before-line-breaking",
  "ly:tie-column::calc-positioning-done",
  "ly:tie::calc-control-points",
  "ly:tie::calc-direction",
  "ly:tie::print",
  "ly:time-signature::print",
  "ly:transform->list",
  "ly:transform?",
  "ly:translate-cpp-warning-scheme",
  "ly:translator-context",
  "ly:translator-description",
  "ly:translator-group?",
  "ly:translator-name",
  "ly:translator?",
  "ly:transpose-key-alist",
  "ly:ttf->pfa",
  "ly:ttf-ps-name",
  "ly:tuplet-bracket::calc-connect-to-neighbors",
  "ly:tuplet-bracket::calc-cross-staff",
  "ly:tuplet-bracket::calc-direction",
  "ly:tuplet-bracket::calc-positions",
  "ly:tuplet-bracket::calc-x-positions",
  "ly:tuplet-bracket::print",
  "ly:tuplet-iterator::constructor",
  "ly:tuplet-number::calc-cross-staff",
  "ly:tuplet-number::calc-x-offset",
  "ly:tuplet-number::calc-y-offset",
  "ly:tuplet-number::print",
  "ly:type1->pfa",
  "ly:unit",
  "ly:unpure-call",
  "ly:unpure-pure-container-pure-part",
  "ly:unpure-pure-container-unpure-part",
  "ly:unpure-pure-container?",
  "ly:usage",
  "ly:vaticana-ligature::brew-ligature-primitive",
  "ly:vaticana-ligature::print",
  "ly:verbose-output?",
  "ly:version",
  "ly:version?",
  "ly:volta-bracket-interface::print",
  "ly:volta-bracket::calc-shorten-pair",
  "ly:volta-repeat-iterator::constructor",
  "ly:volta-specced-music-iterator::constructor",
  "ly:vowel-transition::set-spacing-rods",
  "ly:warning",
  "ly:warning-located",
  "ly:wide-char->utf-8",
  "lyric-hyphen::vaticana-style",
  "lyric-text::print",
  "magnification->font-size",
  "magnify-markup",
  "magnifyStaff-is-set?",
  "magstep",
  "maj7-modifier",
  "make-abs-fontsize-markup",
  "make-accidental-dodecaphonic-rule",
  "make-accidental-markup",
  "make-accidental-rule",
  "make-align-on-other-markup",
  "make-apply-context",
  "make-arrow-head-markup",
  "make-articulation",
  "make-auto-footnote-markup",
  "make-autochange-music",
  "make-backslashed-digit-markup",
  "make-beam-markup",
  "make-bezier-sandwich-stencil",
  "make-bold-markup",
  "make-bow-stencil",
  "make-box-markup",
  "make-bracket-bar-line",
  "make-bracket-markup",
  "make-c-time-signature-markup",
  "make-caps-markup",
  "make-center-align-markup",
  "make-center-column-markup",
  "make-central-column-hole-addresses",
  "make-char-markup",
  "make-chord-elements",
  "make-circle-markup",
  "make-circle-stencil",
  "make-clef-set",
  "make-coda-markup",
  "make-colon-bar-line",
  "make-color-handler",
  "make-column-lines-markup-list",
  "make-column-markup",
  "make-combine-markup",
  "make-compound-meter-markup",
  "make-concat-markup",
  "make-conditional-trill-markup-markup",
  "make-connected-line",
  "make-connected-path-stencil",
  "make-cue-clef-set",
  "make-cue-clef-unset",
  "make-customTabClef-markup",
  "make-dashed-bar-line",
  "make-default-fonts-tree",
  "make-dir-column-markup",
  "make-dotted-bar-line",
  "make-doubleflat-markup",
  "make-doublesharp-markup",
  "make-draw-circle-markup",
  "make-draw-dashed-line-markup",
  "make-draw-dotted-line-markup",
  "make-draw-hline-markup",
  "make-draw-line-markup",
  "make-draw-squiggle-line-markup",
  "make-duration-of-length",
  "make-dynamic-markup",
  "make-ellipse-markup",
  "make-ellipse-stencil",
  "make-empty-bar-line",
  "make-engraver",
  "make-epsfile-markup",
  "make-event-chord",
  "make-extended-scale",
  "make-eyeglasses-markup",
  "make-fermata-markup",
  "make-figured-bass-markup",
  "make-fill-line-markup",
  "make-fill-with-pattern-markup",
  "make-filled-box-markup",
  "make-filled-box-stencil",
  "make-finger-markup",
  "make-first-visible-markup",
  "make-flat-markup",
  "make-font-tree-leaf",
  "make-font-tree-node",
  "make-fontCaps-markup",
  "make-fontsize-markup",
  "make-footnote-markup",
  "make-fraction-markup",
  "make-fret-diagram",
  "make-fret-diagram-markup",
  "make-fret-diagram-terse-markup",
  "make-fret-diagram-verbose-markup",
  "make-fromproperty-markup",
  "make-general-align-markup",
  "make-glyph-time-signature-markup",
  "make-grace-music",
  "make-graceless-rhythmic-location",
  "make-grob-property-override",
  "make-grob-property-revert",
  "make-grob-property-set",
  "make-halign-markup",
  "make-harmonic",
  "make-harp-pedal-markup",
  "make-hbracket-markup",
  "make-hcenter-in-markup",
  "make-hspace-markup",
  "make-huge-markup",
  "make-if-markup",
  "make-italic-markup",
  "make-justified-lines-markup-list",
  "make-justify-field-markup",
  "make-justify-line-markup",
  "make-justify-markup",
  "make-justify-string-markup",
  "make-key-alist",
  "make-key-symbols",
  "make-kievan-bar-line",
  "make-large-markup",
  "make-larger-markup",
  "make-left-align-markup",
  "make-left-brace-markup",
  "make-left-column-markup",
  "make-left-hand-key-addresses",
  "make-line-markup",
  "make-line-stencil",
  "make-lookup-markup",
  "make-lower-markup",
  "make-lyric-event",
  "make-lyric-repeat-count-formatter",
  "make-magnify-markup",
  "make-map-markup-commands-markup-list",
  "make-markalphabet-markup",
  "make-markletter-markup",
  "make-markup",
  "make-medium-markup",
  "make-modal-inverter",
  "make-modal-transposer",
  "make-multi-measure-rest",
  "make-multi-measure-rest-by-number-markup",
  "make-music",
  "make-musicglyph-markup",
  "make-name-keylist",
  "make-named-spreadsheet",
  "make-natural-markup",
  "make-no-bar-line",
  "make-non-relative-music",
  "make-normal-size-sub-markup",
  "make-normal-size-super-markup",
  "make-normal-text-markup",
  "make-normalsize-markup",
  "make-note-by-number-markup",
  "make-note-markup",
  "make-null-markup",
  "make-number-keylist",
  "make-number-markup",
  "make-on-the-fly-markup",
  "make-oval-markup",
  "make-oval-stencil",
  "make-overlay-markup",
  "make-override-lines-markup-list",
  "make-override-markup",
  "make-overtie-markup",
  "make-pad-around-markup",
  "make-pad-markup-markup",
  "make-pad-to-box-markup",
  "make-pad-x-markup",
  "make-page-link-markup",
  "make-page-ref-markup",
  "make-pango-font-tree",
  "make-parenthesis-stencil",
  "make-parenthesize-markup",
  "make-part-combine-context-changes",
  "make-part-combine-marks",
  "make-partial-ellipse-stencil",
  "make-path-markup",
  "make-path-stencil",
  "make-pattern-markup",
  "make-percent-set",
  "make-performer",
  "make-polygon-markup",
  "make-postscript-markup",
  "make-property-recursive-markup",
  "make-property-set",
  "make-property-unset",
  "make-put-adjacent-markup",
  "make-raise-markup",
  "make-relative",
  "make-relative::to-relative-callback",
  "make-repeat",
  "make-replace-markup",
  "make-rest-by-number-markup",
  "make-rest-markup",
  "make-rhythm-markup",
  "make-rhythmic-location",
  "make-right-align-markup",
  "make-right-brace-markup",
  "make-right-column-markup",
  "make-right-hand-key-addresses",
  "make-roman-markup",
  "make-rotate-markup",
  "make-rounded-box-markup",
  "make-sans-markup",
  "make-scale",
  "make-scale-markup",
  "make-score-lines-markup-list",
  "make-score-markup",
  "make-segno-bar-line",
  "make-segno-markup",
  "make-semiflat-markup",
  "make-semisharp-markup",
  "make-semitone->pitch",
  "make-sequential-music",
  "make-sesquiflat-markup",
  "make-sesquisharp-markup",
  "make-session-variable",
  "make-setting",
  "make-sharp-markup",
  "make-short-bar-line",
  "make-simple-bar-line",
  "make-simple-markup",
  "make-simultaneous-music",
  "make-skip-music",
  "make-skipped",
  "make-slashed-digit-markup",
  "make-small-markup",
  "make-smallCaps-markup",
  "make-smaller-markup",
  "make-spacer-bar-line",
  "make-span-event",
  "make-split-state",
  "make-spreadsheet",
  "make-stem-span!",
  "make-stem-spans!",
  "make-stencil-boxer",
  "make-stencil-circler",
  "make-stencil-markup",
  "make-string-lines-markup-list",
  "make-strut-markup",
  "make-sub-markup",
  "make-super-markup",
  "make-symbol-alist",
  "make-tab-heads-transparent",
  "make-table-markup-list",
  "make-teeny-markup",
  "make-text-markup",
  "make-thick-bar-line",
  "make-tick-bar-line",
  "make-tie-markup",
  "make-tie-stencil",
  "make-tied-lyric-markup",
  "make-tilted-portion",
  "make-time-signature-set",
  "make-tiny-markup",
  "make-tmpfile",
  "make-translate-markup",
  "make-translate-scaled-markup",
  "make-translator",
  "make-translator-component",
  "make-translator-internal",
  "make-transparent-box-stencil",
  "make-transparent-markup",
  "make-tremolo-set",
  "make-triangle-markup",
  "make-type-checker",
  "make-typewriter-markup",
  "make-underline-markup",
  "make-undertie-markup",
  "make-unfolded-set",
  "make-unless-markup",
  "make-upright-markup",
  "make-varcoda-markup",
  "make-vcenter-markup",
  "make-verbatim-file-markup",
  "make-voice-props-override",
  "make-voice-props-revert",
  "make-voice-props-set",
  "make-voice-states",
  "make-volta-set",
  "make-vspace-markup",
  "make-whiteout-markup",
  "make-with-color-markup",
  "make-with-dimension-from-markup",
  "make-with-dimension-markup",
  "make-with-dimensions-from-markup",
  "make-with-dimensions-markup",
  "make-with-link-markup",
  "make-with-outline-markup",
  "make-with-string-transformer-markup",
  "make-with-true-dimension-markup",
  "make-with-true-dimensions-markup",
  "make-with-url-markup",
  "make-woodwind-diagram-markup",
  "make-wordwrap-field-markup",
  "make-wordwrap-internal-markup-list",
  "make-wordwrap-lines-markup-list",
  "make-wordwrap-markup",
  "make-wordwrap-string-internal-markup-list",
  "make-wordwrap-string-markup",
  "map-alist-keys",
  "map-alist-vals",
  "map-markup-commands-markup-list",
  "map-selected-alist-keys",
  "map-some-music",
  "markalphabet-markup",
  "marked-up-headfoot",
  "marked-up-title",
  "markgeneric-string",
  "markletter-markup",
  "markup",
  "markup->string",
  "markup-argument-list-error",
  "markup-argument-list?",
  "markup-command-list?",
  "markup-command-signature",
  "markup-default-to-string-method",
  "markup-expression->make-markup",
  "markup-function-as-string-method",
  "markup-function-category",
  "markup-function-properties",
  "markup-function?",
  "markup-join",
  "markup-lambda",
  "markup-lambda-listify",
  "markup-lambda-worker",
  "markup-list-function?",
  "markup-list-lambda",
  "markup-list?",
  "markup-thrower-typecheck",
  "markup-typecheck?",
  "markup?",
  "match-predicate",
  "measure-counter::text",
  "medium-markup",
  "mensural-flag",
  "merge-details",
  "metronome-markup",
  "middle-broken-spanner?",
  "midi-program",
  "midline-stencil",
  "minor-modifier",
  "mkdir-if-not-exist",
  "mm-rest-child-list",
  "mmrest-of-length",
  "modern-straight-flag",
  "modified-font-metric-font-scaling",
  "modulo-bar-number-visible",
  "moment",
  "moment->fraction",
  "moment-min",
  "moment-pair?",
  "moment<=?",
  "move-chord-note",
  "multi-fork",
  "multi-measure-rest-by-number-markup",
  "music->make-music",
  "music-check-error",
  "music-clone",
  "music-filter",
  "music-invert",
  "music-is-of-type?",
  "music-map",
  "music-pitches",
  "music-property-description",
  "music-selective-filter",
  "music-selective-map",
  "music-separator?",
  "music-type-predicate",
  "musicglyph-markup",
  "n-true-entries",
  "narrow-glyph?",
  "natural-chord-alteration",
  "natural-markup",
  "negate-extent",
  "neo-modern-accidental-rule",
  "no-flag",
  "normal-flag",
  "normal-size-sub-markup",
  "normal-size-super-markup",
  "normal-text-markup",
  "normalize-fraction",
  "normalsize-markup",
  "not-first-broken-spanner?",
  "not-last-broken-spanner?",
  "note-by-number-markup",
  "note-events",
  "note-head::brew-ez-stencil",
  "note-head::calc-duration-log",
  "note-head::calc-glyph-name",
  "note-head::calc-kievan-duration-log",
  "note-markup",
  "note-name->german-markup",
  "note-name->markup",
  "note-name->string",
  "note-name-markup",
  "note-names-language",
  "note-to-cluster",
  "notes-to-clusters",
  "null-markup",
  "number->octal-string",
  "number-column-stencil",
  "number-format",
  "number-list?",
  "number-markup",
  "number-or-grob?",
  "number-or-pair?",
  "number-or-string?",
  "number-pair-list?",
  "number-pair?",
  "numbered-footnotes",
  "numerify",
  "object-type",
  "object-type-name",
  "oboe-lh-I-key-stencil",
  "oboe-lh-II-key-stencil",
  "oboe-lh-III-key-stencil",
  "oboe-lh-b-key-stencil",
  "oboe-lh-bes-key-stencil",
  "oboe-lh-cis-key-stencil",
  "oboe-lh-d-key-stencil",
  "oboe-lh-ees-key-stencil",
  "oboe-lh-ees-lh-bes-key-stencil",
  "oboe-lh-f-key-stencil",
  "oboe-lh-gis-key-stencil",
  "oboe-lh-gis-lh-low-b-key-stencil",
  "oboe-lh-low-b-key-stencil",
  "oboe-lh-octave-key-stencil",
  "oboe-rh-a-key-stencil",
  "oboe-rh-banana-key-stencil",
  "oboe-rh-c-key-stencil",
  "oboe-rh-c-rh-ees-key-stencil",
  "oboe-rh-cis-key-stencil",
  "oboe-rh-d-key-stencil",
  "oboe-rh-ees-key-stencil",
  "oboe-rh-f-key-stencil",
  "oboe-rh-gis-key-stencil",
  "octave-woodwind-text-stencil",
  "offset-add",
  "offset-flip-y",
  "offset-fret",
  "offset-multiple-types",
  "offset-scale",
  "offsetter",
  "old-straight-flag",
  "on-the-fly-markup",
  "only-if-beamed",
  "ordered-cons",
  "other-axis",
  "output-module?",
  "output-scopes",
  "outputproperty-compatibility",
  "oval-markup",
  "oval-stencil",
  "overlay-markup",
  "override-head-style",
  "override-lines-markup-list",
  "override-markup",
  "override-property-setting",
  "override-time-signature-setting",
  "overtie-markup",
  "pad-around-markup",
  "pad-markup-markup",
  "pad-to-box-markup",
  "pad-x-markup",
  "page-link-markup",
  "page-ref-markup",
  "pair-map",
  "pango-font-name",
  "pango-pf-file-name",
  "pango-pf-font-name",
  "pango-pf-fontindex",
  "paper-variable",
  "parentheses-interface::calc-angled-bracket-stencils",
  "parentheses-interface::calc-parenthesis-stencils",
  "parentheses-interface::print",
  "parentheses-interface::y-extent",
  "parenthesize-elements",
  "parenthesize-markup",
  "parenthesize-stencil",
  "parse-and-check-version",
  "parse-lily-version",
  "parse-terse-string",
  "path-markup",
  "pattern-markup",
  "percussion?",
  "performance-name-from-headers",
  "piccolo-rh-x-key-stencil",
  "pitch-alteration-semitones",
  "pitch-invert",
  "pitch-of-note",
  "pitch-step",
  "polar->rectangular",
  "polygon-markup",
  "position-true-endpoint",
  "postprocess-output",
  "postscript->pdf",
  "postscript->png",
  "postscript->ps",
  "postscript-markup",
  "precompute-music-length",
  "prepend-alist-chain",
  "prepend-props",
  "pretty-printable?",
  "previous-span-state",
  "previous-voice-state",
  "print-book-with",
  "print-book-with-defaults",
  "print-book-with-defaults-as-systems",
  "print-circled-text-callback",
  "print-keys",
  "print-keys-verbose",
  "process-fill-value",
  "property-recursive-markup",
  "pure-chain-offset-callback",
  "pure-from-neighbor-interface::account-for-span-bar",
  "pure-from-neighbor-interface::extra-spacing-height",
  "pure-from-neighbor-interface::extra-spacing-height-at-beginning-of-line",
  "pure-from-neighbor-interface::extra-spacing-height-including-staff",
  "pure-from-neighbor-interface::pure-height",
  "put-adjacent-markup",
  "quarterdiff->string",
  "quote-substitute",
  "raise-markup",
  "randomize-rand-seed",
  "ratio->fret",
  "ratio->pitch",
  "rational-or-procedure?",
  "read-lily-expression",
  "read-lily-expression-internal",
  "recent-enough?",
  "recompute-music-length",
  "recording-group-emulate",
  "regexp-split",
  "relevant-book-systems",
  "relevant-dump-systems",
  "remove-grace-property",
  "remove-step",
  "remove-whitespace",
  "repeat-tie::handle-tab-note-head",
  "replace-markup",
  "replace-step",
  "replicate-modify",
  "reset-stencil-colors",
  "rest-by-number-markup",
  "rest-markup",
  "retrieve-glyph-flag",
  "retrograde-music",
  "return-1",
  "reverse-interval",
  "revert-fontSize",
  "revert-head-style",
  "revert-property-setting",
  "revert-props",
  "revert-time-signature-setting",
  "rgb-color",
  "rh-woodwind-text-stencil",
  "rhythm-markup",
  "rhythmic-location->file-string",
  "rhythmic-location->string",
  "rhythmic-location-bar-number",
  "rhythmic-location-measure-position",
  "rhythmic-location<=?",
  "rhythmic-location<?",
  "rhythmic-location=?",
  "rhythmic-location>=?",
  "rhythmic-location>?",
  "rhythmic-location?",
  "rich-bassoon-uber-key-stencil",
  "rich-e-stencil",
  "rich-group-draw-rule",
  "rich-group-extra-offset-rule",
  "rich-path-stencil",
  "rich-pe-stencil",
  "right-align-markup",
  "right-brace-markup",
  "right-column-markup",
  "ring-column-circle-stencil",
  "robust-bar-number-function",
  "roman-markup",
  "rotate-markup",
  "rounded-box-markup",
  "rounded-box-stencil",
  "sans-markup",
  "sans-serif-stencil",
  "saxophone-lh-T-key-stencil",
  "saxophone-lh-b-cis-key-stencil",
  "saxophone-lh-b-key-stencil",
  "saxophone-lh-bes-key-stencil",
  "saxophone-lh-cis-key-stencil",
  "saxophone-lh-d-key-stencil",
  "saxophone-lh-ees-key-stencil",
  "saxophone-lh-f-key-stencil",
  "saxophone-lh-front-f-key-stencil",
  "saxophone-lh-gis-key-stencil",
  "saxophone-lh-low-a-key-stencil",
  "saxophone-lh-low-bes-key-stencil",
  "saxophone-name-passerelle",
  "saxophone-rh-bes-key-stencil",
  "saxophone-rh-c-key-stencil",
  "saxophone-rh-e-key-stencil",
  "saxophone-rh-ees-key-stencil",
  "saxophone-rh-fis-key-stencil",
  "saxophone-rh-high-fis-key-stencil",
  "saxophone-rh-low-c-key-stencil",
  "saxophone-rh-side-key-stencil",
  "scale->factor",
  "scale-beam-thickness",
  "scale-by-font-size",
  "scale-fontSize",
  "scale-layout",
  "scale-markup",
  "scale-props",
  "scale?",
  "scheme?",
  "scm->string",
  "score-lines-markup-list",
  "score-markup",
  "scorify-music",
  "script-interface::calc-x-offset",
  "script-or-side-position-cross-staff",
  "search-executable",
  "seconds->moment",
  "segno-markup",
  "select-head-glyph",
  "select-option",
  "self-alignment-interface::self-aligned-on-breakable",
  "self-evaluating?",
  "semi-tie::calc-cross-staff",
  "semiflat-markup",
  "semisharp-markup",
  "sequential-music-to-chord-exceptions",
  "sesquiflat-markup",
  "sesquisharp-markup",
  "session-replay",
  "session-save",
  "session-start-record",
  "session-terminate",
  "set-accidental-style",
  "set-bar-number-visibility",
  "set-counter-text!",
  "set-default-paper-size",
  "set-global-fonts",
  "set-global-staff-size",
  "set-mus-properties!",
  "set-output-property",
  "set-paper-dimension-variables",
  "set-paper-dimensions",
  "set-paper-size",
  "sharp-markup",
  "shift-duration-log",
  "shift-octave",
  "shift-one-duration-log",
  "shift-right-at-line-begin",
  "shift-semitone->pitch",
  "short-glyph?",
  "sign",
  "silence-events",
  "simple-markup",
  "simple-stencil-alist",
  "skip->rest",
  "skip-as-needed",
  "skip-of-length",
  "skip-of-moment-span",
  "skyline-pair-and-non-empty?",
  "skyline-pair::empty?",
  "slashed-digit-internal",
  "slashed-digit-markup",
  "slashify",
  "small-markup",
  "smallCaps-markup",
  "smaller-markup",
  "space-lines",
  "span-bar::compound-bar-line",
  "span-state",
  "split-at-predicate",
  "split-index",
  "split-list",
  "split-list-by-separator",
  "stack-lines",
  "stack-stencil-line",
  "stack-stencils",
  "stack-stencils-padding-list",
  "stack-thirds",
  "staff-ellipsis::calc-y-extent",
  "staff-ellipsis::print",
  "staff-highlight::height",
  "staff-highlight::print",
  "staff-highlight::width",
  "staff-magnification-is-changing?",
  "staff-symbol-line-count",
  "staff-symbol-line-positions",
  "staff-symbol-line-span",
  "staff-symbol-y-extent-from-line-positions",
  "staff-symbol::calc-widened-extent",
  "standard-e-stencil",
  "standard-path-stencil",
  "stderr",
  "stem-connectable?",
  "stem-is-root?",
  "stem-span-stencil",
  "stem-stub::do-calculations",
  "stem-stub::extra-spacing-height",
  "stem-stub::pure-height",
  "stem-stub::width",
  "stem-tremolo::calc-tab-width",
  "stem::calc-duration-log",
  "stem::kievan-offset-callback",
  "stencil-fretboard-extent",
  "stencil-fretboard-offset",
  "stencil-markup",
  "stencil-true-extent",
  "stencil-whiteout",
  "stencil-whiteout-box",
  "stencil-whiteout-outline",
  "stencil-with-color",
  "sticky-grob-interface::inherit-property",
  "straight-flag",
  "string->string-list",
  "string-encode-integer",
  "string-endswith",
  "string-lines-markup-list",
  "string-number::calc-text",
  "string-or-music?",
  "string-or-pair?",
  "string-or-symbol?",
  "string-regexp-substitute",
  "string-startswith",
  "string-thickness",
  "strip-string-annotation",
  "stroke-finger::calc-text",
  "strut-markup",
  "style-note-heads",
  "sub-markup",
  "subtract-base-fret",
  "suggest-convert-ly-message",
  "super-markup",
  "sus-modifier",
  "symbol-concatenate",
  "symbol-footnotes",
  "symbol-key-alist?",
  "symbol-key<?",
  "symbol-list-or-music?",
  "symbol-list-or-symbol?",
  "symbol-list?",
  "symbol<?",
  "symlink-if-not-exist",
  "symlink-or-copy-if-not-exist",
  "symmetric-interval",
  "synced?",
  "system-start-text::calc-x-offset",
  "system-start-text::calc-y-offset",
  "system-start-text::print",
  "tab-note-head::calc-glyph-name",
  "tab-note-head::print",
  "tab-note-head::print-custom-fret-label",
  "tab-note-head::whiteout-if-style-set",
  "tablature-position-on-lines",
  "table-markup-list",
  "tabvoice::draw-double-stem-for-half-notes",
  "tabvoice::make-double-stem-width-for-half-notes",
  "tag-group-get",
  "tags-keep-predicate",
  "tags-remove-predicate",
  "teaching-accidental-rule",
  "teeny-markup",
  "text-fill-translate",
  "text-mark-interface::calc-break-visibility",
  "text-mark-interface::calc-self-alignment-X",
  "text-markup",
  "tie-markup",
  "tie::handle-tab-note-head",
  "tied-lyric-markup",
  "tiny-markup",
  "translate-draw-instructions",
  "translate-key-instruction",
  "translate-markup",
  "translate-scaled-markup",
  "translator-property-description",
  "transparent-markup",
  "transposer-factory",
  "triangle-markup",
  "trill-pitch-group::pure-height",
  "true-entry?",
  "tuning",
  "tuplet-number::append-note-wrapper",
  "tuplet-number::calc-denominator-text",
  "tuplet-number::calc-direction",
  "tuplet-number::calc-fraction-text",
  "tuplet-number::fraction-with-notes",
  "tuplet-number::non-default-fraction-with-notes",
  "tuplet-number::non-default-tuplet-denominator-text",
  "tuplet-number::non-default-tuplet-fraction-text",
  "type-name",
  "typewriter-markup",
  "unbroken-or-first-broken-spanner?",
  "unbroken-or-last-broken-spanner?",
  "unbroken-spanner?",
  "underline-markup",
  "undertie-markup",
  "unfold-repeats",
  "unfold-repeats-fully",
  "uniform-draw-instructions",
  "uniform-extra-offset-rule",
  "uniq-list",
  "uniqued-alist",
  "unity-if-multimeasure",
  "universal-color",
  "unless-markup",
  "update-possb-list",
  "upper-key-stencil",
  "upright-markup",
  "value-for-spanner-piece",
  "varcoda-markup",
  "variable-column-circle-stencil",
  "vcenter-markup",
  "vector-for-each",
  "verbatim-file-markup",
  "version-not-seen-message",
  "voice-states",
  "voicify-chord",
  "voicify-list",
  "voicify-music",
  "void?",
  "volta-bracket-interface::pure-height",
  "volta-bracket::calc-hook-visibility",
  "volta-spec-music",
  "vspace-markup",
  "whiteout-markup",
  "with-color-markup",
  "with-dimension-from-markup",
  "with-dimension-markup",
  "with-dimensions-from-markup",
  "with-dimensions-markup",
  "with-link-markup",
  "with-outline-markup",
  "with-string-transformer-markup",
  "with-true-dimension-markup",
  "with-true-dimensions-markup",
  "with-url-markup",
  "woodwind-diagram-markup",
  "wordwrap-field-markup",
  "wordwrap-internal-markup-list",
  "wordwrap-lines-markup-list",
  "wordwrap-markup",
  "wordwrap-stencils",
  "wordwrap-string-internal-markup-list",
  "wordwrap-string-markup",
  "write-lilypond-book-aux-files",
  "write-me",
  "write-performances-midis",
  "x11-color",
]

context_properties = [
  "aDueText",
  "accidentalGrouping",
  "additionalBassStrings",
  "additionalPitchPrefix",
  "alignAboveContext",
  "alignBelowContext",
  "alterationGlyphs",
  "alternativeNumber",
  "alternativeNumberingStyle",
  "alternativeRestores",
  "associatedVoice",
  "associatedVoiceContext",
  "associatedVoiceType",
  "autoAccidentals",
  "autoBeamCheck",
  "autoBeaming",
  "autoCautionaries",
  "barCheckLastFail",
  "barCheckSynchronize",
  "barExtraVelocity",
  "barNumberFormatter",
  "barNumberVisibility",
  "baseMoment",
  "beamExceptions",
  "beamHalfMeasure",
  "beamMelismaBusy",
  "beatExtraVelocity",
  "beatStructure",
  "breathMarkDefinitions",
  "breathMarkType",
  "busyGrobs",
  "caesuraType",
  "caesuraTypeTransform",
  "centerBarNumbers",
  "chordChanges",
  "chordNameExceptions",
  "chordNameFunction",
  "chordNameLowercaseMinor",
  "chordNameSeparator",
  "chordNoteNamer",
  "chordPrefixSpacer",
  "chordRootNamer",
  "clefGlyph",
  "clefPosition",
  "clefTransposition",
  "clefTranspositionFormatter",
  "clefTranspositionStyle",
  "codaMarkCount",
  "codaMarkFormatter",
  "completionBusy",
  "completionFactor",
  "completionUnit",
  "connectArpeggios",
  "countPercentRepeats",
  "createKeyOnClefChange",
  "createSpacing",
  "crescendoSpanner",
  "crescendoText",
  "cueClefGlyph",
  "cueClefPosition",
  "cueClefTransposition",
  "cueClefTranspositionFormatter",
  "cueClefTranspositionStyle",
  "currentBarLine",
  "currentBarNumber",
  "currentChordCause",
  "currentChordText",
  "currentCommandColumn",
  "currentMusicalColumn",
  "currentPerformanceMarkEvent",
  "currentRehearsalMarkEvent",
  "dalSegnoTextFormatter",
  "decrescendoSpanner",
  "decrescendoText",
  "defaultStrings",
  "doubleRepeatBarType",
  "doubleRepeatSegnoBarType",
  "doubleSlurs",
  "drumPitchTable",
  "drumStyleTable",
  "dynamicAbsoluteVolumeFunction",
  "endAtSkip",
  "endRepeatBarType",
  "endRepeatSegnoBarType",
  "explicitClefVisibility",
  "explicitCueClefVisibility",
  "explicitKeySignatureVisibility",
  "extendersOverRests",
  "extraNatural",
  "figuredBassAlterationDirection",
  "figuredBassCenterContinuations",
  "figuredBassFormatter",
  "figuredBassLargeNumberAlignment",
  "figuredBassPlusDirection",
  "figuredBassPlusStrokedAlist",
  "finalFineTextVisibility",
  "finalizations",
  "fineBarType",
  "fineSegnoBarType",
  "fineStartRepeatSegnoBarType",
  "fineText",
  "fingeringOrientations",
  "firstClef",
  "followVoice",
  "fontSize",
  "forbidBreak",
  "forbidBreakBetweenBarLines",
  "forceBreak",
  "forceClef",
  "fretLabels",
  "glissandoMap",
  "graceSettings",
  "gridInterval",
  "handleNegativeFrets",
  "harmonicAccidentals",
  "harmonicDots",
  "hasAxisGroup",
  "hasStaffSpacing",
  "highStringOne",
  "ignoreBarChecks",
  "ignoreBarNumberChecks",
  "ignoreFiguredBassRest",
  "ignoreMelismata",
  "implicitBassFigures",
  "includeGraceNotes",
  "initialTimeSignatureVisibility",
  "instrumentCueName",
  "instrumentEqualizer",
  "instrumentName",
  "instrumentTransposition",
  "internalBarNumber",
  "keepAliveInterfaces",
  "keyAlterationOrder",
  "keyAlterations",
  "lastChord",
  "lastKeyAlterations",
  "localAlterations",
  "lyricMelismaAlignment",
  "lyricRepeatCountFormatter",
  "magnifyStaffValue",
  "majorSevenSymbol",
  "maximumFretStretch",
  "measureBarType",
  "measureLength",
  "measurePosition",
  "measureStartNow",
  "melismaBusy",
  "melismaBusyProperties",
  "metronomeMarkFormatter",
  "middleCClefPosition",
  "middleCCuePosition",
  "middleCOffset",
  "middleCPosition",
  "midiBalance",
  "midiChannelMapping",
  "midiChorusLevel",
  "midiExpression",
  "midiInstrument",
  "midiMaximumVolume",
  "midiMergeUnisons",
  "midiMinimumVolume",
  "midiPanPosition",
  "midiReverbLevel",
  "midiSkipOffset",
  "minimumFret",
  "minimumPageTurnLength",
  "minimumRepeatLengthForPageTurn",
  "minorChordModifier",
  "noChordSymbol",
  "noteNameFunction",
  "noteNameSeparator",
  "noteToFretFunction",
  "nullAccidentals",
  "ottavaStartNow",
  "ottavation",
  "ottavationMarkups",
  "output",
  "partCombineForced",
  "partCombineTextsOnNote",
  "partialBusy",
  "pedalSostenutoStrings",
  "pedalSostenutoStyle",
  "pedalSustainStrings",
  "pedalSustainStyle",
  "pedalUnaCordaStrings",
  "pedalUnaCordaStyle",
  "predefinedDiagramTable",
  "printAccidentalNames",
  "printKeyCancellation",
  "printNotesLanguage",
  "printOctaveNames",
  "printPartCombineTexts",
  "proportionalNotationDuration",
  "quotedCueEventTypes",
  "quotedEventTypes",
  "rehearsalMark",
  "rehearsalMarkFormatter",
  "repeatCommands",
  "repeatCountVisibility",
  "restCompletionBusy",
  "restNumberThreshold",
  "restrainOpenStrings",
  "rootSystem",
  "scriptDefinitions",
  "searchForVoice",
  "sectionBarType",
  "segnoBarType",
  "segnoMarkCount",
  "segnoMarkFormatter",
  "segnoStyle",
  "shapeNoteStyles",
  "shortInstrumentName",
  "shortVocalName",
  "skipBars",
  "skipTypesetting",
  "slashChordSeparator",
  "slurMelismaBusy",
  "soloIIText",
  "soloText",
  "squashedPosition",
  "staffLineLayoutFunction",
  "stanza",
  "startAtNoteColumn",
  "startAtSkip",
  "startRepeatBarType",
  "startRepeatSegnoBarType",
  "stavesFound",
  "stemLeftBeamCount",
  "stemRightBeamCount",
  "strictBeatBeaming",
  "stringFretFingerList",
  "stringNumberOrientations",
  "stringOneTopmost",
  "stringTunings",
  "strokeFingerOrientations",
  "subdivideBeams",
  "suggestAccidentals",
  "supportNonIntegerFret",
  "suspendMelodyDecisions",
  "suspendRestMerging",
  "systemStartDelimiter",
  "systemStartDelimiterHierarchy",
  "tabStaffLineLayoutFunction",
  "tablatureFormat",
  "tempoHideNote",
  "tempoWholesPerMinute",
  "tieMelismaBusy",
  "tieWaitForNote",
  "timeSignatureFraction",
  "timeSignatureSettings",
  "timing",
  "tonic",
  "topLevelAlignment",
  "tupletFullLength",
  "tupletFullLengthNote",
  "tupletSpannerDuration",
  "underlyingRepeatBarType",
  "useBassFigureExtenders",
  "vocalName",
  "voltaSpannerDuration",
  "whichBar",
]

grob_properties = [
  "X-align-on-main-noteheads",
  "X-attachment",
  "X-common",
  "X-extent",
  "X-offset",
  "X-positions",
  "Y-attachment",
  "Y-common",
  "Y-extent",
  "Y-offset",
  "accidental-grob",
  "accidental-grobs",
  "add-cauda",
  "add-join",
  "add-stem",
  "add-stem-support",
  "adjacent-pure-heights",
  "adjacent-spanners",
  "after-line-breaking",
  "align-dir",
  "all-elements",
  "allow-loose-spacing",
  "allow-span-bar",
  "alteration",
  "alteration-alist",
  "alteration-glyph-name-alist",
  "annotation",
  "annotation-balloon",
  "annotation-line",
  "arpeggio-direction",
  "arrow-length",
  "arrow-width",
  "ascendens",
  "auctum",
  "auto-knee-gap",
  "automatically-numbered",
  "average-spacing-wishes",
  "avoid-note-head",
  "avoid-scripts",
  "avoid-slur",
  "axes",
  "axis-group-parent-X",
  "axis-group-parent-Y",
  "bar-extent",
  "bars",
  "base-shortest-duration",
  "baseline-skip",
  "beam",
  "beam-segments",
  "beam-thickness",
  "beam-width",
  "beamed-stem-shorten",
  "beaming",
  "beamlet-default-length",
  "beamlet-max-length-proportion",
  "before-line-breaking",
  "begin-of-line-visible",
  "bend-me",
  "between-cols",
  "bezier",
  "bound-alignment-interfaces",
  "bound-details",
  "bound-padding",
  "bound-prefatory-paddings",
  "bounded-by-me",
  "bracket",
  "bracket-flare",
  "bracket-text",
  "bracket-visibility",
  "break-align-anchor",
  "break-align-anchor-alignment",
  "break-align-orders",
  "break-align-symbol",
  "break-align-symbols",
  "break-alignment",
  "break-overshoot",
  "break-visibility",
  "breakable",
  "broken-bound-padding",
  "c0-position",
  "cause",
  "cavum",
  "chord-dots-limit",
  "chord-names",
  "circled-tip",
  "clef-alignments",
  "clip-edges",
  "collapse-height",
  "collision-interfaces",
  "collision-voice-only",
  "color",
  "columns",
  "common-shortest-duration",
  "concaveness",
  "concurrent-hairpins",
  "conditional-elements",
  "connect-to-neighbor",
  "context-info",
  "control-points",
  "count-from",
  "covered-grobs",
  "cross-staff",
  "damping",
  "dash-definition",
  "dash-fraction",
  "dash-period",
  "dashed-edge",
  "default-direction",
  "default-staff-staff-spacing",
  "delta-position",
  "deminutum",
  "descendens",
  "details",
  "digit-names",
  "direction",
  "direction-source",
  "display-cautionary",
  "dot",
  "dot-count",
  "dot-negative-kern",
  "dot-placement-list",
  "dot-stencil",
  "dots",
  "double-stem-separation",
  "duration-log",
  "eccentricity",
  "edge-height",
  "edge-text",
  "elements",
  "encompass-objects",
  "endpoint-alignments",
  "expand-limit",
  "extra-dy",
  "extra-offset",
  "extra-spacing-height",
  "extra-spacing-width",
  "extroversion",
  "fa-merge-direction",
  "fa-styles",
  "figures",
  "filled",
  "flag",
  "flag-count",
  "flag-style",
  "flat-positions",
  "flexa-height",
  "flexa-interval",
  "flexa-width",
  "font",
  "font-encoding",
  "font-family",
  "font-features",
  "font-name",
  "font-series",
  "font-shape",
  "font-size",
  "footnote",
  "footnote-music",
  "footnote-stencil",
  "footnote-text",
  "footnotes-after-line-breaking",
  "footnotes-before-line-breaking",
  "force-hshift",
  "forced",
  "forced-spacing",
  "fraction",
  "french-beaming",
  "french-beaming-stem-adjustment",
  "fret-diagram-details",
  "full-length-padding",
  "full-length-to-extent",
  "full-measure-extra-space",
  "full-size-change",
  "gap",
  "gap-count",
  "glissando-index",
  "glissando-skip",
  "glyph",
  "glyph-left",
  "glyph-name",
  "glyph-right",
  "grace-spacing",
  "graphical",
  "grow-direction",
  "hair-thickness",
  "harp-pedal-details",
  "has-span-bar",
  "head-direction",
  "head-width",
  "heads",
  "height",
  "height-limit",
  "hide-tied-accidental-after-break",
  "horizon-padding",
  "horizontal-shift",
  "horizontal-skylines",
  "id",
  "ideal-distances",
  "ignore-ambitus",
  "ignore-collision",
  "implicit",
  "important-column-ranks",
  "in-note-direction",
  "in-note-padding",
  "in-note-stencil",
  "inclinatum",
  "index",
  "inspect-quants",
  "interfaces",
  "items-worth-living",
  "keep-alive-with",
  "keep-inside-line",
  "kern",
  "knee",
  "knee-spacing-correction",
  "knee-to-beam",
  "labels",
  "layer",
  "least-squares-dy",
  "ledger-extra",
  "ledger-line-thickness",
  "ledger-positions",
  "ledger-positions-function",
  "left-bound-info",
  "left-items",
  "left-neighbor",
  "left-number-text",
  "left-padding",
  "length",
  "length-fraction",
  "ligature-flexa",
  "line-break-penalty",
  "line-break-permission",
  "line-break-system-details",
  "line-count",
  "line-positions",
  "line-thickness",
  "linea",
  "long-text",
  "main-extent",
  "make-dead-when",
  "max-beam-connect",
  "max-symbol-separation",
  "maximum-gap",
  "maybe-loose",
  "measure-count",
  "measure-division",
  "measure-division-chord-placement-alist",
  "measure-division-lines-alist",
  "measure-length",
  "melody-spanner",
  "merge-differently-dotted",
  "merge-differently-headed",
  "meta",
  "minimum-X-extent",
  "minimum-Y-extent",
  "minimum-distance",
  "minimum-distances",
  "minimum-length",
  "minimum-length-after-break",
  "minimum-length-fraction",
  "minimum-space",
  "minimum-translations-alist",
  "neighbors",
  "neutral-direction",
  "neutral-position",
  "next",
  "no-ledgers",
  "no-stem-extend",
  "non-break-align-symbols",
  "non-default",
  "non-musical",
  "nonstaff-nonstaff-spacing",
  "nonstaff-relatedstaff-spacing",
  "nonstaff-unrelatedstaff-spacing",
  "normal-stems",
  "normalized-endpoints",
  "note-collision",
  "note-collision-threshold",
  "note-columns",
  "note-head",
  "note-heads",
  "note-names",
  "number-range-separator",
  "number-type",
  "numbering-assertion-function",
  "oriscus",
  "output-attributes",
  "outside-staff-horizontal-padding",
  "outside-staff-padding",
  "outside-staff-placement-directive",
  "outside-staff-priority",
  "packed-spacing",
  "padding",
  "padding-pairs",
  "page-break-penalty",
  "page-break-permission",
  "page-number",
  "page-turn-penalty",
  "page-turn-permission",
  "parent-alignment-X",
  "parent-alignment-Y",
  "parenthesis-friends",
  "parenthesis-id",
  "parenthesized",
  "pedal-text",
  "pes-or-flexa",
  "positioning-done",
  "positions",
  "prefer-dotted-right",
  "prefix-set",
  "primitive",
  "protrusion",
  "pure-Y-common",
  "pure-Y-extent",
  "pure-Y-offset-in-progress",
  "pure-relevant-grobs",
  "pure-relevant-items",
  "pure-relevant-spanners",
  "quantize-position",
  "quantized-positions",
  "quilisma",
  "rank-on-page",
  "ratio",
  "remove-empty",
  "remove-first",
  "remove-layer",
  "replacement-alist",
  "rest",
  "rest-collision",
  "restore-first",
  "rests",
  "rhythmic-location",
  "right-bound-info",
  "right-items",
  "right-neighbor",
  "right-number-text",
  "right-padding",
  "rotation",
  "round-up-exceptions",
  "round-up-to-longer-rest",
  "rounded",
  "same-direction-correction",
  "script-column",
  "script-priority",
  "script-stencil",
  "scripts",
  "segno-kern",
  "self-alignment-X",
  "self-alignment-Y",
  "shape",
  "sharp-positions",
  "shorten",
  "shorten-pair",
  "shortest-duration-space",
  "shortest-playing-duration",
  "shortest-starter-duration",
  "show-control-points",
  "show-horizontal-skylines",
  "show-vertical-skylines",
  "side-axis",
  "side-relative-direction",
  "side-support-elements",
  "size",
  "skip-quanting",
  "skyline-horizontal-padding",
  "skyline-vertical-padding",
  "slash-negative-kern",
  "slope",
  "slur",
  "slur-padding",
  "snap-radius",
  "space-alist",
  "space-increment",
  "space-to-barline",
  "spacing",
  "spacing-increment",
  "spacing-pair",
  "spacing-wishes",
  "span-start",
  "spanner-broken",
  "spanner-id",
  "spanner-placement",
  "springs-and-rods",
  "stacking-dir",
  "staff-affinity",
  "staff-grouper",
  "staff-padding",
  "staff-position",
  "staff-space",
  "staff-staff-spacing",
  "staff-symbol",
  "staffgroup-staff-spacing",
  "stem",
  "stem-attachment",
  "stem-begin-position",
  "stem-info",
  "stem-spacing-correction",
  "stemlet-length",
  "stems",
  "stencil",
  "stencils",
  "sticky-host",
  "strict-grace-spacing",
  "strict-note-spacing",
  "stroke-style",
  "stropha",
  "style",
  "system-Y-offset",
  "text",
  "text-alignment-X",
  "text-alignment-Y",
  "text-direction",
  "thick-thickness",
  "thickness",
  "tie",
  "tie-configuration",
  "ties",
  "to-barline",
  "toward-stem-shift",
  "toward-stem-shift-in-column",
  "transparent",
  "tremolo-flag",
  "tuplet-number",
  "tuplet-slur",
  "tuplet-start",
  "tuplets",
  "uniform-stretching",
  "usable-duration-logs",
  "use-skylines",
  "used",
  "vertical-alignment",
  "vertical-skyline-elements",
  "vertical-skylines",
  "virga",
  "visible-over-note-heads",
  "voiced-position",
  "when",
  "whiteout",
  "whiteout-style",
  "widened-extent",
  "width",
  "woodwind-diagram-details",
  "word-space",
  "x-offset",
  "zigzag-length",
  "zigzag-width",
]

paper_variables = [
  "auto-first-page-number",
  "basic-distance",
  "binding-offset",
  "blank-last-page-penalty",
  "blank-page-penalty",
  "bookTitleMarkup",
  "bottom-margin",
  "check-consistency",
  "evenFooterMarkup",
  "evenHeaderMarkup",
  "first-page-number",
  "footnote-separator-markup",
  "horizontal-shift",
  "indent",
  "inner-margin",
  "last-bottom-spacing",
  "left-margin",
  "line-width",
  "markup-markup-spacing",
  "markup-system-spacing",
  "max-systems-per-page",
  "min-systems-per-page",
  "minimum-distance",
  "oddFooterMarkup",
  "oddHeaderMarkup",
  "outer-margin",
  "padding",
  "page-breaking",
  "page-breaking-system-system-spacing",
  "page-count",
  "page-number-type",
  "page-spacing-weight",
  "paper-height",
  "paper-width",
  "print-all-headers",
  "print-first-page-number",
  "ragged-bottom",
  "ragged-last",
  "ragged-last-bottom",
  "ragged-right",
  "right-margin",
  "score-markup-spacing",
  "score-system-spacing",
  "scoreTitleMarkup",
  "short-indent",
  "stretchability",
  "system-count",
  "system-separator-markup",
  "system-system-spacing",
  "systems-per-page",
  "top-margin",
  "top-markup-spacing",
  "top-system-spacing",
  "two-sided",
]

header_variables = [
  "arranger",
  "composer",
  "copyright",
  "dedication",
  "doctitle",
  "instrument",
  "lsrtags",
  "meter",
  "opus",
  "piece",
  "poet",
  "subsubtitle",
  "subtitle",
  "tagline",
  "texidoc",
  "title",
]


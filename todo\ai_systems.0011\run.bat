@echo off
setlocal enabledelayedexpansion

:: Change to script directory
cd /d "%~dp0"

:: Check if uv is installed
where uv >nul 2>&1
if errorlevel 1 (
    echo Error: uv is not installed or not in PATH
    echo Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

:: Sync dependencies
echo Syncing dependencies with uv...
uv sync
if errorlevel 1 (
    echo Error: Failed to sync dependencies
    pause
    exit /b 1
)

:: Run the application
echo.
echo Running AI Systems...
echo.
uv run python src/main.py %*

:: Keep window open if run without arguments
if "%~1"=="" (
    echo.
    pause
)

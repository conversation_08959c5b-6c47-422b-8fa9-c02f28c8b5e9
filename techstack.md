# Technology Stack

## Core
- **Python 3.9+** - Primary language
- **uv** - Modern dependency management (target)
- **pyproject.toml** - Project configuration

## Dependencies
- **litellm** - LLM API abstraction
- **pydantic** - Data validation
- **loguru** - Structured logging
- **openai** - OpenAI API client
- **jinja2** - Template engine
- **python-dotenv** - Environment management
- **tqdm** - Progress bars
- **pyyaml** - YAML processing

## Structure Pattern
```
project_name/
├── pyproject.toml
├── uv.lock
├── run.bat
├── packages_upgrade.bat
├── src/
│   └── main.py
└── README.md
```
